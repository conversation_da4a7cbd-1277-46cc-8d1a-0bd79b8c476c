<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>產品渲染測試</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css?v=20250812-fix-admin-tabs">
</head>
<body>
    <div class="container mt-4">
        <h1>🧪 產品渲染測試</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>測試控制</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testRender()">測試渲染</button>
                        <button class="btn btn-success" onclick="testRealAPI()">測試真實API</button>
                        <button class="btn btn-info" onclick="clearProducts()">清除產品</button>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>測試資料</h5>
                    </div>
                    <div class="card-body">
                        <pre id="test-data" style="font-size: 12px; max-height: 200px; overflow-y: auto;">
點擊按鈕開始測試...
                        </pre>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>渲染結果</h5>
                    </div>
                    <div class="card-body">
                        <div id="render-result">
                            <p class="text-muted">產品將在這裡顯示...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>產品網格測試</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="products-grid-container">
                            <div class="products-grid-header">
                                <div class="grid-header-item">健保資訊</div>
                                <div class="grid-header-item">品名 / 成分</div>
                                <div class="grid-header-item">規格</div>
                                <div class="grid-header-item">單價</div>
                                <div class="grid-header-item">數量</div>
                                <div class="grid-header-item">功能</div>
                                <div class="grid-header-item">狀態</div>
                            </div>
                            <div id="products-grid-body" class="products-grid-body">
                                <!-- 產品卡片將在這裡顯示 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/config.js"></script>
    <script>
        const API_BASE = window.CONFIG ? window.CONFIG.API_BASE : "http://localhost:8080";
        
        // 模擬產品資料
        const mockProducts = [
            {
                id: 1,
                name: "測試產品1",
                nhi_code: "A001",
                dosage_form: "100mg",
                unit_price: 50,
                stock_quantity: 100,
                nhi_price: 45
            },
            {
                id: 2,
                name: "測試產品2",
                nhi_code: "A002",
                dosage_form: "200mg",
                unit_price: 75,
                stock_quantity: 50,
                nhi_price: 70
            }
        ];
        
        // 模擬用戶資料
        const mockUser = {
            username: "test_user",
            permissions: {
                role_name: "pharmacy"
            }
        };
        
        // 測試渲染函數
        function testRender() {
            console.log("開始測試產品渲染...");
            
            try {
                // 測試DOM元素是否存在
                const gridBody = document.getElementById("products-grid-body");
                if (!gridBody) {
                    throw new Error("找不到products-grid-body元素");
                }
                
                console.log("找到products-grid-body元素:", gridBody);
                
                // 測試CSS樣式
                const computedStyle = window.getComputedStyle(gridBody);
                console.log("products-grid-body樣式:", {
                    display: computedStyle.display,
                    visibility: computedStyle.visibility,
                    height: computedStyle.height,
                    width: computedStyle.width
                });
                
                // 渲染產品
                renderProducts(mockProducts);
                
                console.log("產品渲染測試完成");
                
            } catch (error) {
                console.error("產品渲染測試失敗:", error);
                showResult(`渲染測試失敗: ${error.message}`, 'danger');
            }
        }
        
        // 測試真實API
        async function testRealAPI() {
            try {
                console.log("開始測試真實API...");
                
                const response = await fetch(`${API_BASE}/api/products?page=1&limit=5`);
                const data = await response.json();
                
                if (data.success) {
                    console.log("API產品資料:", data);
                    showResult(`API測試成功: 載入${data.data.length}筆產品`, 'success');
                    
                    // 渲染真實產品
                    renderProducts(data.data);
                } else {
                    throw new Error(data.error || "API回應錯誤");
                }
                
            } catch (error) {
                console.error("API測試失敗:", error);
                showResult(`API測試失敗: ${error.message}`, 'danger');
            }
        }
        
        // 清除產品
        function clearProducts() {
            const gridBody = document.getElementById("products-grid-body");
            if (gridBody) {
                gridBody.innerHTML = '<div class="no-data" style="text-align: center; padding: 40px; color: #999;">產品已清除</div>';
            }
            
            const renderResult = document.getElementById("render-result");
            renderResult.innerHTML = '<p class="text-muted">產品已清除...</p>';
        }
        
        // 渲染產品函數
        function renderProducts(products) {
            const gridBody = document.getElementById("products-grid-body");
            const renderResult = document.getElementById("render-result");
            
            if (!gridBody) {
                throw new Error("找不到products-grid-body元素");
            }
            
            if (products.length === 0) {
                gridBody.innerHTML = '<div class="no-data" style="text-align: center; padding: 40px; color: #999;">沒有找到產品</div>';
                renderResult.innerHTML = '<p class="text-muted">沒有產品資料</p>';
                return;
            }
            
            // 更新測試資料顯示
            document.getElementById('test-data').textContent = JSON.stringify(products, null, 2);
            
            // 渲染到產品網格
            const gridHTML = products.map(product => createProductCard(product)).join("");
            gridBody.innerHTML = gridHTML;
            
            // 渲染到結果區域
            const resultHTML = products.map(product => `
                <div class="card mb-2">
                    <div class="card-body">
                        <h6 class="card-title">${product.name}</h6>
                        <p class="card-text">
                            健保代碼: ${product.nhi_code || 'N/A'}<br>
                            規格: ${product.dosage_form || 'N/A'}<br>
                            價格: ${product.unit_price || 'N/A'}
                        </p>
                    </div>
                </div>
            `).join("");
            renderResult.innerHTML = resultHTML;
            
            console.log(`產品渲染完成，共${products.length}筆`);
        }
        
        // 創建產品卡片
        function createProductCard(product) {
            return `
                <div class="product-card">
                    <div class="nhi-info">
                        <div class="nhi-details">
                            <div class="nhi-code">${product.nhi_code || "N/A"}</div>
                            <div class="nhi-level">健保價: ${product.nhi_price || "N/A"}</div>
                            <div class="nhi-expiry">2027.04.30</div>
                        </div>
                    </div>
                    
                    <div class="product-name">
                        <div class="product-title">${product.name || "N/A"}</div>
                        <div class="product-ingredients">主要成分</div>
                    </div>
                    
                    <div class="product-dosage">
                        <div class="dosage-form">${product.dosage_form || "N/A"}</div>
                    </div>
                    
                    <div class="product-price">
                        <div class="price-value">${product.unit_price || "0"}</div>
                        <div class="price-unit">/元</div>
                    </div>
                    
                    <div class="product-quantity">
                        <input type="number" class="quantity-input" min="1" value="1">
                        <div class="quantity-label">訂量</div>
                    </div>
                    
                    <div class="product-actions">
                        <button class="action-btn cart" title="加入購物車">🛒</button>
                        <button class="action-btn favorite" title="收藏">❤️</button>
                    </div>
                    
                    <div class="product-status">
                        <span class="status-badge in-stock">供貨中</span>
                    </div>
                </div>
            `;
        }
        
        // 顯示結果
        function showResult(message, type = 'info') {
            const result = document.getElementById("render-result");
            result.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
        }
        
        // 頁面載入完成後執行
        document.addEventListener('DOMContentLoaded', function() {
            console.log('產品渲染測試頁面載入完成');
            console.log('products-grid-body元素:', document.getElementById('products-grid-body'));
        });
    </script>
</body>
</html>
