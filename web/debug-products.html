<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>產品載入調試</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>產品載入調試</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>API測試</h5>
                    </div>
                    <div class="card-body">
                                <button class="btn btn-primary" onclick="testAPI()">測試API連線</button>
        <button class="btn btn-success" onclick="testProducts()">測試產品載入</button>
        <button class="btn btn-info" onclick="testAuth()">測試認證狀態</button>
        <button class="btn btn-warning" onclick="testApiConnection()">詳細API測試</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>結果</h5>
                    </div>
                    <div class="card-body">
                        <div id="result" class="alert alert-info">
                            點擊按鈕開始測試...
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>產品列表</h5>
                    </div>
                    <div class="card-body">
                        <div id="products-list">
                            <!-- 產品將在這裡顯示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/config.js"></script>
    <script src="api-fix.js"></script>
    <script>
        const API_BASE = window.CONFIG ? window.CONFIG.API_BASE : "http://localhost:8080";
        
        function showResult(message, type = 'info') {
            const result = document.getElementById('result');
            result.className = `alert alert-${type}`;
            result.textContent = message;
        }
        
        async function testAPI() {
            try {
                showResult('測試API連線中...', 'info');
                
                const response = await fetch(`${API_BASE}/api/health`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`API連線成功: ${JSON.stringify(data)}`, 'success');
                } else {
                    showResult(`API連線失敗: ${response.status}`, 'danger');
                }
            } catch (error) {
                showResult(`API連線錯誤: ${error.message}`, 'danger');
            }
        }
        
        async function testProducts() {
            try {
                showResult('載入產品中...', 'info');
                
                const response = await fetch(`${API_BASE}/api/products?page=1&limit=10`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`產品載入成功: 共${data.data?.length || 0}筆`, 'success');
                    displayProducts(data.data || []);
                } else {
                    showResult(`產品載入失敗: ${data.error || response.status}`, 'danger');
                }
            } catch (error) {
                showResult(`產品載入錯誤: ${error.message}`, 'danger');
            }
        }
        
        async function testAuth() {
            try {
                const token = localStorage.getItem('authToken');
                const user = localStorage.getItem('user');
                
                if (token && user) {
                    const userData = JSON.parse(user);
                    showResult(`已登入: ${userData.username}`, 'success');
                } else {
                    showResult('未登入', 'warning');
                }
            } catch (error) {
                showResult(`認證檢查錯誤: ${error.message}`, 'danger');
            }
        }
        
        function displayProducts(products) {
            const container = document.getElementById('products-list');
            
            if (products.length === 0) {
                container.innerHTML = '<p class="text-muted">沒有產品資料</p>';
                return;
            }
            
            const html = products.map(product => `
                <div class="card mb-2">
                    <div class="card-body">
                        <h6 class="card-title">${product.name || 'N/A'}</h6>
                        <p class="card-text">
                            健保代碼: ${product.nhi_code || 'N/A'}<br>
                            規格: ${product.dosage_form || 'N/A'}<br>
                            價格: ${product.unit_price || 'N/A'}
                        </p>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }
        
        // 頁面載入時自動測試
        document.addEventListener('DOMContentLoaded', function() {
            showResult('頁面載入完成，請點擊按鈕開始測試', 'info');
        });
    </script>
</body>
</html>
