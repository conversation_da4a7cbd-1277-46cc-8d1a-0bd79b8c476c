<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>台南社區藥局藥品供應平台 - 簡化版</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <!-- 只載入基本的 CSS，不載入可能有問題的自定義 CSS -->
    <style>
        body {
            background-color: #f5f5f5;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .bg-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <header class="bg-gradient rounded-3 text-white p-4 mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <img src="images/logo.png" alt="公司標誌" class="rounded me-3" style="height: 50px;">
                    <div>
                        <h1 class="mb-1">台南社區藥局藥品供應平台</h1>
                        <p class="mb-0 text-light">行政院衛生署補(捐)助科技發展計畫</p>
                    </div>
                </div>
            </div>
        </header>

        <!-- 測試內容 -->
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-info">
                        <h4>系統狀態檢查</h4>
                        <p>如果您能看到這個頁面，表示基本的 HTML 和 Bootstrap 正常工作。</p>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5>登入測試</h5>
                        </div>
                        <div class="card-body">
                            <form>
                                <div class="mb-3">
                                    <label for="username" class="form-label">使用者名稱:</label>
                                    <input type="text" id="username" class="form-control" placeholder="請輸入使用者名稱">
                                </div>
                                <div class="mb-3">
                                    <label for="password" class="form-label">密碼:</label>
                                    <input type="password" id="password" class="form-control" placeholder="請輸入密碼">
                                </div>
                                <button type="button" class="btn btn-primary" onclick="testLogin()">測試登入</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (username && password) {
                alert('測試登入成功！用戶名: ' + username);
            } else {
                alert('請輸入用戶名和密碼');
            }
        }
        
        console.log('簡化版頁面載入完成');
        console.log('Bootstrap 狀態:', window.bootstrap ? '已載入' : '未載入');
        
        // 檢查是否有 JavaScript 錯誤
        window.addEventListener('error', function(e) {
            console.error('JavaScript 錯誤:', e.error);
            alert('發現 JavaScript 錯誤: ' + e.message);
        });
    </script>
</body>
</html>
