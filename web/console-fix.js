// 控制台直接執行的修復腳本
// 複製這段程式碼到瀏覽器控制台執行

(function() {
    console.log('🔧 開始修復主頁面產品顯示問題...');
    
    try {
        // 1. 檢查DOM元素
        const gridBody = document.getElementById('products-grid-body');
        if (!gridBody) {
            console.error('❌ 找不到products-grid-body元素');
            return;
        }
        console.log('✅ 找到products-grid-body元素:', gridBody);
        
        // 2. 檢查產品資料
        if (typeof currentProducts === 'undefined') {
            console.error('❌ currentProducts變數未定義');
            return;
        }
        console.log('✅ currentProducts:', currentProducts);
        
        // 3. 強制重新渲染
        console.log('🔄 強制重新渲染產品...');
        
        // 清除現有內容
        gridBody.innerHTML = '';
        
        // 檢查是否有產品資料
        if (currentProducts && currentProducts.length > 0) {
            console.log(`📦 開始渲染 ${currentProducts.length} 筆產品...`);
            
            // 使用簡化的渲染邏輯
            const html = currentProducts.map(product => `
                <div class="product-card">
                    <div class="nhi-info">
                        <div class="nhi-details">
                            <div class="nhi-code">${product.nhi_code || "N/A"}</div>
                            <div class="nhi-level">健保價: ${product.nhi_price || "N/A"}</div>
                            <div class="nhi-expiry">2027.04.30</div>
                        </div>
                    </div>
                    
                    <div class="product-name">
                        <div class="product-title">${product.name || "N/A"}</div>
                        <div class="product-ingredients">主要成分</div>
                    </div>
                    
                    <div class="product-dosage">
                        <div class="dosage-form">${product.dosage_form || "N/A"}</div>
                    </div>
                    
                    <div class="product-price">
                        <div class="price-value">${product.unit_price || "0"}</div>
                        <div class="price-unit">/元</div>
                    </div>
                    
                    <div class="product-quantity">
                        <input type="number" class="quantity-input" min="1" value="1">
                        <div class="quantity-label">訂量</div>
                    </div>
                    
                    <div class="product-actions">
                        <button class="action-btn cart" title="加入購物車">🛒</button>
                        <button class="action-btn favorite" title="收藏">❤️</button>
                    </div>
                    
                    <div class="product-status">
                        <span class="status-badge in-stock">供貨中</span>
                    </div>
                </div>
            `).join("");
            
            gridBody.innerHTML = html;
            console.log('✅ 產品渲染完成');
            
            // 更新分頁資訊
            const paginationInfo = document.getElementById('pagination-info');
            if (paginationInfo) {
                const total = currentProducts.length;
                paginationInfo.textContent = `顯示第 1-${total} 筆，共 ${total} 筆資料`;
            }
            
        } else {
            console.log('⚠️ 沒有產品資料可顯示');
            gridBody.innerHTML = '<div class="no-data" style="text-align: center; padding: 40px; color: #999;">沒有找到產品</div>';
        }
        
    } catch (error) {
        console.error('❌ 修復過程發生錯誤:', error);
    }
})();
