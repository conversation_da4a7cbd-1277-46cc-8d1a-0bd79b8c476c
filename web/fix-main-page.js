// 主頁面產品顯示修復腳本
console.log('🔧 開始修復主頁面產品顯示問題...');

// 修復函數
function fixMainPageProducts() {
    console.log('檢查主頁面產品顯示問題...');
    
    try {
        // 1. 檢查DOM元素
        const gridBody = document.getElementById('products-grid-body');
        if (!gridBody) {
            console.error('❌ 找不到products-grid-body元素');
            return false;
        }
        console.log('✅ 找到products-grid-body元素:', gridBody);
        
        // 2. 檢查CSS樣式
        const computedStyle = window.getComputedStyle(gridBody);
        console.log('CSS樣式檢查:', {
            display: computedStyle.display,
            visibility: computedStyle.visibility,
            height: computedStyle.height,
            width: computedStyle.width,
            opacity: computedStyle.opacity
        });
        
        // 3. 檢查產品資料
        if (typeof currentProducts === 'undefined') {
            console.error('❌ currentProducts變數未定義');
            return false;
        }
        console.log('✅ currentProducts:', currentProducts);
        
        // 4. 強制重新渲染
        console.log('🔄 強制重新渲染產品...');
        
        // 清除現有內容
        gridBody.innerHTML = '';
        
        // 檢查是否有產品資料
        if (currentProducts && currentProducts.length > 0) {
            console.log(`📦 開始渲染 ${currentProducts.length} 筆產品...`);
            
            // 使用簡化的渲染邏輯
            const html = currentProducts.map(product => `
                <div class="product-card">
                    <div class="nhi-info">
                        <div class="nhi-details">
                            <div class="nhi-code">${product.nhi_code || "N/A"}</div>
                            <div class="nhi-level">健保價: ${product.nhi_price || "N/A"}</div>
                            <div class="nhi-expiry">2027.04.30</div>
                        </div>
                    </div>
                    
                    <div class="product-name">
                        <div class="product-title">${product.name || "N/A"}</div>
                        <div class="product-ingredients">主要成分</div>
                    </div>
                    
                    <div class="product-dosage">
                        <div class="dosage-form">${product.dosage_form || "N/A"}</div>
                    </div>
                    
                    <div class="product-price">
                        <div class="price-value">${product.unit_price || "0"}</div>
                        <div class="price-unit">/元</div>
                    </div>
                    
                    <div class="product-quantity">
                        <input type="number" class="quantity-input" min="1" value="1">
                        <div class="quantity-label">訂量</div>
                    </div>
                    
                    <div class="product-actions">
                        <button class="action-btn cart" title="加入購物車" onclick="addToCart(${product.id}, this)">🛒</button>
                        <button class="action-btn favorite" title="收藏" onclick="toggleFavorite(${product.id})">❤️</button>
                    </div>
                    
                    <div class="product-status">
                        <span class="status-badge in-stock">供貨中</span>
                    </div>
                </div>
            `).join("");
            
            gridBody.innerHTML = html;
            console.log('✅ 產品渲染完成');
            
            // 更新分頁資訊
            updatePaginationInfo();
            
            return true;
        } else {
            console.log('⚠️ 沒有產品資料可顯示');
            gridBody.innerHTML = '<div class="no-data" style="text-align: center; padding: 40px; color: #999;">沒有找到產品</div>';
            return false;
        }
        
    } catch (error) {
        console.error('❌ 修復過程發生錯誤:', error);
        return false;
    }
}

// 更新分頁資訊
function updatePaginationInfo() {
    try {
        const paginationInfo = document.getElementById('pagination-info');
        if (paginationInfo && currentProducts) {
            const total = currentProducts.length;
            paginationInfo.textContent = `顯示第 1-${total} 筆，共 ${total} 筆資料`;
        }
    } catch (error) {
        console.error('更新分頁資訊失敗:', error);
    }
}

// 檢查並修復CSS問題
function fixCSSIssues() {
    console.log('🔧 檢查CSS問題...');
    
    const gridBody = document.getElementById('products-grid-body');
    if (gridBody) {
        // 強制設定樣式
        gridBody.style.display = 'block';
        gridBody.style.visibility = 'visible';
        gridBody.style.opacity = '1';
        
        console.log('✅ CSS樣式已修復');
    }
}

// 監聽頁面載入完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 主頁面DOM載入完成');
    
    // 等待一下讓其他腳本執行完成
    setTimeout(() => {
        console.log('⏰ 延遲執行修復...');
        fixCSSIssues();
        
        // 如果已經有產品資料，立即修復
        if (typeof currentProducts !== 'undefined' && currentProducts && currentProducts.length > 0) {
            console.log('🚀 發現產品資料，立即修復顯示');
            fixMainPageProducts();
        }
    }, 1000);
});

// 監聽標籤頁切換
document.addEventListener('click', function(e) {
    if (e.target && e.target.getAttribute('data-tab') === 'products') {
        console.log('🔄 產品標籤被點擊，檢查並修復...');
        
        // 延遲執行，等待標籤頁切換完成
        setTimeout(() => {
            fixMainPageProducts();
        }, 100);
    }
});

// 導出修復函數供手動調用
window.fixMainPageProducts = fixMainPageProducts;
window.fixCSSIssues = fixCSSIssues;

console.log('🔧 主頁面產品顯示修復腳本載入完成');
console.log('💡 如果產品仍然不顯示，請在控制台執行: fixMainPageProducts()');
