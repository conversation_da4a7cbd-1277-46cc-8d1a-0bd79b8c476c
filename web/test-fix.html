<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>測試修復</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
</head>
<body>
    <div class="container mt-4">
        <h1>Bootstrap標籤頁測試</h1>
        
        <ul class="nav nav-tabs" id="testTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="tab1-tab" data-bs-toggle="tab" data-bs-target="#tab1" type="button" role="tab" aria-controls="tab1" aria-selected="true">標籤1</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="tab2-tab" data-bs-toggle="tab" data-bs-target="#tab2" type="button" role="tab" aria-controls="tab2" aria-selected="false">標籤2</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="tab3-tab" data-bs-toggle="tab" data-bs-target="#tab3" type="button" role="tab" aria-controls="tab3" aria-selected="false">標籤3</button>
            </li>
        </ul>
        
        <div class="tab-content" id="testTabContent">
            <div class="tab-pane fade show active" id="tab1" role="tabpanel" aria-labelledby="tab1-tab">
                <div class="card mt-3">
                    <div class="card-body">
                        <h5 class="card-title">標籤1內容</h5>
                        <p class="card-text">這是第一個標籤的內容。</p>
                    </div>
                </div>
            </div>
            <div class="tab-pane fade" id="tab2" role="tabpanel" aria-labelledby="tab2-tab">
                <div class="card mt-3">
                    <div class="card-body">
                        <h5 class="card-title">標籤2內容</h5>
                        <p class="card-text">這是第二個標籤的內容。</p>
                    </div>
                </div>
            </div>
            <div class="tab-pane fade" id="tab3" role="tabpanel" aria-labelledby="tab3-tab">
                <div class="card mt-3">
                    <div class="card-body">
                        <h5 class="card-title">標籤3內容</h5>
                        <p class="card-text">這是第三個標籤的內容。</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <button class="btn btn-primary" onclick="testTabSwitch()">測試標籤切換</button>
        </div>
    </div>

    <!-- Bootstrap 5 JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function testTabSwitch() {
            // 測試Bootstrap標籤頁API
            const tab2 = document.getElementById('tab2-tab');
            const tab = new bootstrap.Tab(tab2);
            tab.show();
            console.log('標籤切換測試完成');
        }
        
        // 測試事件監聽器
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM載入完成');
            
            // 為標籤添加點擊事件
            document.querySelectorAll('#testTabs .nav-link').forEach(tab => {
                tab.addEventListener('click', function() {
                    console.log('標籤被點擊:', this.textContent);
                });
            });
        });
    </script>
</body>
</html>
