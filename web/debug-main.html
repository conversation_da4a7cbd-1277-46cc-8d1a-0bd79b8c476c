<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主頁面調試</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/style.css?v=20250812-fix-admin-tabs">
</head>

<body>
    <div class="container-fluid">
        <header class="bg-gradient rounded-3 text-white p-4 mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <img src="images/logo.png" alt="公司標誌" class="rounded me-3" style="height: 50px;">
                    <div>
                        <h1 class="mb-1">台南社區藥局藥品供應平台</h1>
                        <p class="mb-0 text-light">行政院衛生署補(捐)助科技發展計畫</p>
                    </div>
                </div>
                <div id="user-info" class="user-info" style="display: none;">
                    <span id="user-display-name" class="me-3"></span>
                    <button id="logout-btn" class="btn btn-light">登出</button>
                </div>
            </div>
        </header>

        <!-- 主要內容區域 -->
        <div id="main-content" class="section flex-grow-1">
            <ul class="nav nav-tabs" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="products-tab" data-bs-toggle="tab" data-bs-target="#products-tab-content" 
                            type="button" role="tab" data-tab="products">產品管理</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="cart-tab" data-bs-toggle="tab" data-bs-target="#cart-tab-content" 
                            type="button" role="tab" data-tab="cart">🛒 購物車</button>
                </li>
            </ul>

            <div class="tab-content mt-3" id="mainTabContent">

            <!-- 產品管理 -->
            <div id="products-tab-content" class="tab-pane fade show active" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h3 class="mb-0">🏥 查詢中心產品目錄</h3>
                            <div class="d-flex gap-2">
                                <div class="input-group" style="width: 300px;">
                                    <input type="text" id="product-search" class="form-control" 
                                           placeholder="輸入產品名稱、健保代碼或製造商...">
                                    <button id="search-btn" class="btn btn-primary">查詢</button>
                                    <button id="clear-search-btn" class="btn btn-outline-secondary">清除</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">

                    <div class="products-grid-container">
                        <div class="products-grid-header">
                            <div class="grid-header-item">健保資訊</div>
                            <div class="grid-header-item">品名 / 成分</div>
                            <div class="grid-header-item">規格</div>
                            <div class="grid-header-item">單價</div>
                            <div class="grid-header-item">數量</div>
                            <div class="grid-header-item">功能</div>
                            <div class="grid-header-item">狀態</div>
                        </div>
                        <div id="products-grid-body" class="products-grid-body">
                            <!-- 產品卡片將在這裡動態載入 -->
                        </div>
                    </div>

                    <div class="table-pagination">
                        <div class="pagination-info">
                            <span id="pagination-info">顯示第 1-10 筆，共 0 筆資料</span>
                        </div>
                        <div class="pagination-controls">
                            <button id="prev-page" class="btn btn-secondary">上一頁</button>
                            <div class="page-input-group">
                                <span>第</span>
                                <input type="number" id="page-input" min="1" value="1"
                                    style="width: 60px; text-align: center;">
                                <span>頁</span>
                                <button id="goto-page" class="btn btn-primary">前往</button>
                            </div>
                            <button id="next-page" class="btn btn-secondary">下一頁</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 購物車 -->
            <div id="cart-tab-content" class="tab-pane fade" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h3 class="mb-0">🛒 購物車</h3>
                    </div>
                    <div class="card-body">
                        <div id="cart-items">
                            <!-- 購物車項目將在這裡動態載入 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 載入指示器 -->
        <div id="loading" class="loading" style="display: none;">
            <div class="spinner"></div>
            <p>載入中...</p>
        </div>

        <!-- 訊息提示 -->
        <div id="message" class="message" style="display: none;"></div>
    </div>

    <!-- Bootstrap 5 JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/config.js"></script>
    
    <script>
        // 模擬產品資料
        const mockProducts = [
            {
                id: 1,
                name: "測試產品1",
                nhi_code: "A001",
                dosage_form: "100mg",
                unit_price: 50,
                stock_quantity: 100
            },
            {
                id: 2,
                name: "測試產品2",
                nhi_code: "A002",
                dosage_form: "200mg",
                unit_price: 75,
                stock_quantity: 50
            }
        ];

        // 顯示產品
        function displayProducts(products) {
            const gridBody = document.getElementById("products-grid-body");
            if (!gridBody) {
                console.error("找不到products-grid-body元素");
                return;
            }

            if (products.length === 0) {
                gridBody.innerHTML = '<div class="no-data" style="text-align: center; padding: 40px; color: #999;">沒有找到產品</div>';
                return;
            }

            const html = products.map(product => `
                <div class="product-card">
                    <div class="nhi-info">
                        <div class="nhi-details">
                            <div class="nhi-code">${product.nhi_code}</div>
                            <div class="nhi-level">健保價: N/A</div>
                            <div class="nhi-expiry">2027.04.30</div>
                        </div>
                    </div>
                    <div class="product-name">
                        <div class="product-title">${product.name}</div>
                        <div class="product-ingredients">主要成分</div>
                    </div>
                    <div class="product-dosage">
                        <div class="dosage-form">${product.dosage_form}</div>
                    </div>
                    <div class="product-price">
                        <div class="price">NT$ ${product.unit_price}</div>
                    </div>
                    <div class="product-quantity">
                        <input type="number" min="1" value="1" class="quantity-input">
                    </div>
                    <div class="product-actions">
                        <button class="btn btn-primary btn-sm">加入購物車</button>
                    </div>
                    <div class="product-status">
                        <span class="status-badge in-stock">有庫存</span>
                    </div>
                </div>
            `).join("");

            gridBody.innerHTML = html;
            console.log("產品顯示完成，共", products.length, "筆");
        }

        // 頁面載入完成後執行
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM載入完成，開始調試...');
            
            // 檢查元素是否存在
            const elements = {
                'products-grid-body': document.getElementById('products-grid-body'),
                'products-tab': document.getElementById('products-tab'),
                'cart-tab': document.getElementById('cart-tab')
            };
            
            console.log('元素檢查結果:', elements);
            
            // 顯示測試產品
            displayProducts(mockProducts);
            
            // 測試標籤切換
            document.querySelectorAll('#mainTabs .nav-link').forEach(tab => {
                tab.addEventListener('click', function() {
                    console.log('標籤被點擊:', this.textContent);
                });
            });
        });
    </script>
</body>
</html>
