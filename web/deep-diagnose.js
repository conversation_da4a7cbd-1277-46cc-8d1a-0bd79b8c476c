// 深度診斷產品顯示問題
console.log('🔍 開始深度診斷產品顯示問題...');

(function() {
    try {
        // 1. 檢查DOM元素
        const gridBody = document.getElementById('products-grid-body');
        console.log('1. DOM元素檢查:');
        console.log('   - gridBody存在:', !!gridBody);
        console.log('   - gridBody類型:', gridBody ? gridBody.tagName : 'N/A');
        console.log('   - gridBody ID:', gridBody ? gridBody.id : 'N/A');
        console.log('   - gridBody類別:', gridBody ? gridBody.className : 'N/A');
        
        if (!gridBody) {
            console.error('❌ 找不到products-grid-body元素');
            return;
        }
        
        // 2. 檢查HTML內容
        console.log('2. HTML內容檢查:');
        console.log('   - innerHTML長度:', gridBody.innerHTML.length);
        console.log('   - 子元素數量:', gridBody.children.length);
        console.log('   - 第一個子元素:', gridBody.children[0]);
        
        // 3. 檢查CSS樣式
        console.log('3. CSS樣式檢查:');
        const style = window.getComputedStyle(gridBody);
        console.log('   - display:', style.display);
        console.log('   - visibility:', style.visibility);
        console.log('   - opacity:', style.opacity);
        console.log('   - height:', style.height);
        console.log('   - width:', style.width);
        console.log('   - position:', style.position);
        console.log('   - zIndex:', style.zIndex);
        console.log('   - overflow:', style.overflow);
        
        // 4. 檢查父元素
        console.log('4. 父元素檢查:');
        let parent = gridBody.parentElement;
        let level = 1;
        while (parent && level <= 5) {
            const parentStyle = window.getComputedStyle(parent);
            console.log(`   - 父元素${level} (${parent.tagName}.${parent.className}):`);
            console.log(`     display: ${parentStyle.display}`);
            console.log(`     visibility: ${parentStyle.visibility}`);
            console.log(`     height: ${parentStyle.height}`);
            console.log(`     width: ${parentStyle.width}`);
            console.log(`     overflow: ${parentStyle.overflow}`);
            console.log(`     position: ${parentStyle.position}`);
            
            if (parentStyle.display === 'none' || parentStyle.visibility === 'hidden') {
                console.error(`❌ 發現問題！父元素${level}被隱藏`);
            }
            
            parent = parent.parentElement;
            level++;
        }
        
        // 5. 檢查產品資料
        console.log('5. 產品資料檢查:');
        console.log('   - currentProducts類型:', typeof currentProducts);
        console.log('   - currentProducts長度:', currentProducts ? currentProducts.length : 'N/A');
        if (currentProducts && currentProducts.length > 0) {
            console.log('   - 第一個產品:', currentProducts[0]);
        }
        
        // 6. 強制測試顯示
        console.log('6. 強制測試顯示:');
        
        // 創建一個非常簡單的測試元素
        const testDiv = document.createElement('div');
        testDiv.innerHTML = `
            <div style="
                background: red;
                color: white;
                padding: 20px;
                margin: 10px;
                border: 3px solid blue;
                font-size: 18px;
                font-weight: bold;
            ">
                🚨 測試產品顯示 - 如果你看到這個紅色方塊，說明容器是正常的！
            </div>
        `;
        
        // 插入到products-grid-body
        gridBody.appendChild(testDiv);
        console.log('   - 已插入測試元素');
        
        // 7. 檢查測試元素是否可見
        setTimeout(() => {
            console.log('7. 測試元素檢查:');
            const testElement = gridBody.querySelector('div');
            if (testElement) {
                const testStyle = window.getComputedStyle(testElement);
                console.log('   - 測試元素樣式:', {
                    display: testStyle.display,
                    visibility: testStyle.visibility,
                    height: testStyle.height,
                    width: testStyle.width,
                    backgroundColor: testStyle.backgroundColor
                });
                
                // 檢查元素是否在視窗內
                const rect = testElement.getBoundingClientRect();
                console.log('   - 測試元素位置:', {
                    top: rect.top,
                    left: rect.left,
                    width: rect.width,
                    height: rect.height,
                    inViewport: rect.top >= 0 && rect.left >= 0 && rect.bottom <= window.innerHeight && rect.right <= window.innerWidth
                });
            }
        }, 100);
        
        // 8. 檢查頁面滾動
        console.log('8. 頁面滾動檢查:');
        console.log('   - 頁面滾動位置:', window.pageYOffset);
        console.log('   - 視窗高度:', window.innerHeight);
        console.log('   - 文檔高度:', document.documentElement.scrollHeight);
        
        // 9. 檢查是否有其他元素覆蓋
        console.log('9. 元素覆蓋檢查:');
        const allElements = document.querySelectorAll('*');
        let coveringElements = [];
        
        for (let i = 0; i < allElements.length; i++) {
            const element = allElements[i];
            const style = window.getComputedStyle(element);
            if (style.position === 'absolute' || style.position === 'fixed') {
                const rect = element.getBoundingClientRect();
                if (rect.width > 0 && rect.height > 0) {
                    coveringElements.push({
                        element: element,
                        tagName: element.tagName,
                        className: element.className,
                        id: element.id,
                        position: style.position,
                        zIndex: style.zIndex,
                        rect: rect
                    });
                }
            }
        }
        
        console.log('   - 絕對定位元素數量:', coveringElements.length);
        if (coveringElements.length > 0) {
            console.log('   - 前5個絕對定位元素:', coveringElements.slice(0, 5));
        }
        
        console.log('✅ 深度診斷完成！請查看上面的詳細資訊');
        
    } catch (error) {
        console.error('❌ 深度診斷失敗:', error);
    }
})();
