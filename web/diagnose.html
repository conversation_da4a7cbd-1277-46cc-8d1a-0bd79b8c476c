<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API問題診斷</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        .log-error { color: #dc3545; }
        .log-success { color: #198754; }
        .log-info { color: #0dcaf0; }
        .log-warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>🔍 API問題診斷工具</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>診斷測試</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="runFullDiagnosis()">執行完整診斷</button>
                            <button class="btn btn-success" onclick="testHealthEndpoint()">測試健康檢查</button>
                            <button class="btn btn-info" onclick="testProductsEndpoint()">測試產品端點</button>
                            <button class="btn btn-warning" onclick="testAuthEndpoint()">測試認證端點</button>
                            <button class="btn btn-secondary" onclick="clearLogs()">清除日誌</button>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>環境資訊</h5>
                    </div>
                    <div class="card-body">
                        <div id="env-info">
                            <p><strong>當前URL:</strong> <span id="current-url"></span></p>
                            <p><strong>API基礎URL:</strong> <span id="api-base"></span></p>
                            <p><strong>用戶代理:</strong> <span id="user-agent"></span></p>
                            <p><strong>本地儲存:</strong> <span id="local-storage"></span></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>診斷結果</h5>
                    </div>
                    <div class="card-body">
                        <div id="diagnosis-results">
                            <div class="alert alert-info">
                                點擊按鈕開始診斷...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>詳細日誌</h5>
                    </div>
                    <div class="card-body">
                        <div id="logs" class="log-container">
                            <!-- 日誌將在這裡顯示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/config.js"></script>
    <script>
        const API_BASE = window.CONFIG ? window.CONFIG.API_BASE : "http://localhost:8080";
        
        // 日誌函數
        function log(message, type = 'info') {
            const logsContainer = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
            log('日誌已清除', 'info');
        }
        
        // 顯示結果
        function showResult(message, type = 'info') {
            const results = document.getElementById('diagnosis-results');
            results.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
        }
        
        // 更新環境資訊
        function updateEnvInfo() {
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('api-base').textContent = API_BASE;
            document.getElementById('user-agent').textContent = navigator.userAgent;
            
            const storageInfo = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                storageInfo.push(`${key}: ${value ? '已設定' : '未設定'}`);
            }
            document.getElementById('local-storage').textContent = storageInfo.length > 0 ? storageInfo.join(', ') : '無';
        }
        
        // 測試健康檢查端點
        async function testHealthEndpoint() {
            log('開始測試健康檢查端點...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/health`);
                log(`健康檢查回應狀態: ${response.status} ${response.statusText}`, 'info');
                
                const contentType = response.headers.get('content-type');
                log(`回應內容類型: ${contentType}`, 'info');
                
                const responseText = await response.text();
                log(`回應內容長度: ${responseText.length}`, 'info');
                
                if (responseText.length > 0) {
                    log(`回應內容預覽: ${responseText.substring(0, 200)}...`, 'info');
                    
                    if (contentType && contentType.includes('application/json')) {
                        try {
                            const data = JSON.parse(responseText);
                            log('JSON解析成功', 'success');
                            showResult(`健康檢查成功: ${JSON.stringify(data)}`, 'success');
                        } catch (error) {
                            log(`JSON解析失敗: ${error.message}`, 'error');
                            showResult('健康檢查回應格式錯誤', 'warning');
                        }
                    } else {
                        log('回應不是JSON格式', 'warning');
                        showResult('健康檢查回應不是JSON格式', 'warning');
                    }
                } else {
                    log('回應為空', 'error');
                    showResult('健康檢查回應為空', 'danger');
                }
                
            } catch (error) {
                log(`健康檢查測試失敗: ${error.message}`, 'error');
                showResult(`健康檢查失敗: ${error.message}`, 'danger');
            }
        }
        
        // 測試產品端點
        async function testProductsEndpoint() {
            log('開始測試產品端點...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/products?page=1&limit=5`);
                log(`產品端點回應狀態: ${response.status} ${response.statusText}`, 'info');
                
                const contentType = response.headers.get('content-type');
                log(`回應內容類型: ${contentType}`, 'info');
                
                const responseText = await response.text();
                log(`回應內容長度: ${responseText.length}`, 'info');
                
                if (responseText.length > 0) {
                    log(`回應內容預覽: ${responseText.substring(0, 200)}...`, 'info');
                    
                    if (contentType && contentType.includes('application/json')) {
                        try {
                            const data = JSON.parse(responseText);
                            log('JSON解析成功', 'success');
                            
                            if (data.success) {
                                const productCount = data.data ? data.data.length : 0;
                                showResult(`產品端點成功: 載入${productCount}筆產品`, 'success');
                            } else {
                                showResult(`產品端點回應錯誤: ${data.error}`, 'warning');
                            }
                        } catch (error) {
                            log(`JSON解析失敗: ${error.message}`, 'error');
                            showResult('產品端點回應格式錯誤', 'warning');
                        }
                    } else {
                        log('回應不是JSON格式', 'warning');
                        showResult('產品端點回應不是JSON格式', 'warning');
                    }
                } else {
                    log('回應為空', 'error');
                    showResult('產品端點回應為空', 'danger');
                }
                
            } catch (error) {
                log(`產品端點測試失敗: ${error.message}`, 'error');
                showResult(`產品端點失敗: ${error.message}`, 'danger');
            }
        }
        
        // 測試認證端點
        async function testAuthEndpoint() {
            log('開始測試認證端點...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/auth/status`);
                log(`認證端點回應狀態: ${response.status} ${response.statusText}`, 'info');
                
                const contentType = response.headers.get('content-type');
                log(`回應內容類型: ${contentType}`, 'info');
                
                const responseText = await response.text();
                log(`回應內容長度: ${responseText.length}`, 'info');
                
                if (responseText.length > 0) {
                    log(`回應內容預覽: ${responseText.substring(0, 200)}...`, 'info');
                    
                    if (contentType && contentType.includes('application/json')) {
                        try {
                            const data = JSON.parse(responseText);
                            log('JSON解析成功', 'success');
                            showResult(`認證端點成功: ${JSON.stringify(data)}`, 'success');
                        } catch (error) {
                            log(`JSON解析失敗: ${error.message}`, 'error');
                            showResult('認證端點回應格式錯誤', 'warning');
                        }
                    } else {
                        log('回應不是JSON格式', 'warning');
                        showResult('認證端點回應不是JSON格式', 'warning');
                    }
                } else {
                    log('回應為空', 'error');
                    showResult('認證端點回應為空', 'danger');
                }
                
            } catch (error) {
                log(`認證端點測試失敗: ${error.message}`, 'error');
                showResult(`認證端點失敗: ${error.message}`, 'danger');
            }
        }
        
        // 執行完整診斷
        async function runFullDiagnosis() {
            log('開始執行完整診斷...', 'info');
            showResult('診斷進行中...', 'info');
            
            // 更新環境資訊
            updateEnvInfo();
            
            // 測試各個端點
            await testHealthEndpoint();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testProductsEndpoint();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testAuthEndpoint();
            
            log('完整診斷完成', 'success');
            showResult('診斷完成，請查看詳細日誌', 'success');
        }
        
        // 頁面載入時自動更新環境資訊
        document.addEventListener('DOMContentLoaded', function() {
            log('診斷工具載入完成', 'success');
            updateEnvInfo();
        });
    </script>
</body>
</html>
