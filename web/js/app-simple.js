// 簡化版的應用程式 JavaScript
console.log('簡化版 JavaScript 載入');

// 基本的 DOM 元素
let loginSection, registerSection, mainContent, userInfo;

// 初始化函數
document.addEventListener("DOMContentLoaded", function () {
    console.log('DOM 已載入，開始初始化...');
    
    // 初始化DOM元素
    loginSection = document.getElementById("login-section");
    registerSection = document.getElementById("register-section");
    mainContent = document.getElementById("main-content");
    userInfo = document.getElementById("user-info");
    
    console.log('DOM元素初始化完成:', {
        loginSection: !!loginSection,
        registerSection: !!registerSection,
        mainContent: !!mainContent,
        userInfo: !!userInfo
    });
    
    // 顯示登入頁面
    showLogin();
    
    // 設置基本事件監聽器
    setupBasicEventListeners();
    
    console.log('簡化版應用程式初始化完成');
});

// 顯示登入頁面
function showLogin() {
    console.log('顯示登入頁面');
    if (loginSection) loginSection.style.display = "block";
    if (registerSection) registerSection.style.display = "none";
    if (mainContent) mainContent.style.display = "none";
    if (userInfo) userInfo.style.display = "none";
}

// 顯示註冊頁面
function showRegister() {
    console.log('顯示註冊頁面');
    if (loginSection) loginSection.style.display = "none";
    if (registerSection) registerSection.style.display = "block";
    if (mainContent) mainContent.style.display = "none";
    if (userInfo) userInfo.style.display = "none";
}

// 顯示主要內容
function showMainContent() {
    console.log('顯示主要內容');
    if (loginSection) loginSection.style.display = "none";
    if (registerSection) registerSection.style.display = "none";
    if (mainContent) mainContent.style.display = "block";
    if (userInfo) userInfo.style.display = "block";
}

// 設置基本事件監聽器
function setupBasicEventListeners() {
    console.log('設置事件監聽器');
    
    // 登入表單
    const loginForm = document.getElementById("login-form");
    if (loginForm) {
        loginForm.addEventListener("submit", function(e) {
            e.preventDefault();
            console.log('登入表單提交');
            alert('登入功能暫時停用，這是測試版本');
        });
    }
    
    // 註冊連結
    const showRegisterLink = document.getElementById("show-register");
    if (showRegisterLink) {
        showRegisterLink.addEventListener("click", function(e) {
            e.preventDefault();
            showRegister();
        });
    }
    
    // 返回登入連結
    const showLoginLink = document.getElementById("show-login");
    if (showLoginLink) {
        showLoginLink.addEventListener("click", function(e) {
            e.preventDefault();
            showLogin();
        });
    }
    
    // 註冊表單
    const registerForm = document.getElementById("register-form");
    if (registerForm) {
        registerForm.addEventListener("submit", function(e) {
            e.preventDefault();
            console.log('註冊表單提交');
            alert('註冊功能暫時停用，這是測試版本');
        });
    }
    
    console.log('事件監聽器設置完成');
}

// 錯誤處理
window.addEventListener('error', function(e) {
    console.error('JavaScript 錯誤:', e.error);
    console.error('錯誤位置:', e.filename, '行號:', e.lineno);
});

console.log('簡化版 JavaScript 檔案載入完成');
