// API請求修復腳本
// 解決 "Unexpected end of JSON input" 錯誤

// 修復後的API請求函數
async function fixedApiRequest(url, options = {}) {
    const fullUrl = `${API_BASE}${url}`;
    console.log("發送 API 請求到:", fullUrl);

    const config = {
        headers: {
            "Content-Type": "application/json",
            ...options.headers,
        },
        ...options,
    };

    // 添加認證 token
    if (authToken && isTokenValid()) {
        config.headers["Authorization"] = `Bearer ${authToken}`;
    }

    try {
        const response = await fetch(fullUrl, config);
        console.log(`API回應狀態: ${response.status} ${response.statusText}`);

        // 檢查回應類型
        const contentType = response.headers.get("content-type");
        console.log(`回應內容類型: ${contentType}`);

        // 檢查回應是否為空
        const responseText = await response.text();
        console.log(`回應內容長度: ${responseText.length}`);
        console.log(`回應內容預覽: ${responseText.substring(0, 200)}...`);

        // 如果回應為空，返回錯誤
        if (!responseText || responseText.trim() === '') {
            throw new Error("API回應為空");
        }

        // 嘗試解析JSON
        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error("JSON解析失敗:", parseError);
            console.error("原始回應:", responseText);
            
            // 如果是HTML錯誤頁面，提取有用資訊
            if (responseText.includes('<html') || responseText.includes('<!DOCTYPE')) {
                throw new Error("API返回了HTML頁面，可能是伺服器錯誤");
            }
            
            throw new Error(`API回應格式錯誤: ${parseError.message}`);
        }

        if (!response.ok) {
            throw new Error(data.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        return data;
    } catch (error) {
        console.error("API請求失敗:", error);
        
        // 網路錯誤
        if (error.name === "TypeError") {
            throw new Error("網路連線失敗，請檢查網路連線");
        }
        
        // 其他錯誤
        throw error;
    }
}

// 測試API連線的函數
async function testApiConnection() {
    console.log("開始測試API連線...");
    
    try {
        // 測試健康檢查端點
        console.log("測試健康檢查...");
        const healthResponse = await fetch(`${API_BASE}/api/health`);
        console.log("健康檢查狀態:", healthResponse.status);
        
        if (healthResponse.ok) {
            const healthText = await healthResponse.text();
            console.log("健康檢查回應:", healthText);
        }
        
        // 測試產品端點
        console.log("測試產品端點...");
        const productsResponse = await fetch(`${API_BASE}/api/products?page=1&limit=5`);
        console.log("產品端點狀態:", productsResponse.status);
        
        if (productsResponse.ok) {
            const productsText = await productsResponse.text();
            console.log("產品端點回應長度:", productsText.length);
            console.log("產品端點回應預覽:", productsText.substring(0, 200));
            
            // 嘗試解析JSON
            try {
                const productsData = JSON.parse(productsText);
                console.log("產品資料解析成功:", productsData);
            } catch (parseError) {
                console.error("產品資料JSON解析失敗:", parseError);
            }
        } else {
            console.error("產品端點錯誤:", productsResponse.status, productsResponse.statusText);
        }
        
    } catch (error) {
        console.error("API連線測試失敗:", error);
    }
}

// 修復產品載入函數
async function loadProductsFixed(page = 1) {
    try {
        console.log(`載入第 ${page} 頁產品...`);
        
        const params = new URLSearchParams();
        params.append("page", page.toString());
        params.append("limit", "10");
        
        const url = `/api/products?${params}`;
        console.log(`API請求URL: ${url}`);
        
        const response = await fixedApiRequest(url);
        
        if (response.success) {
            console.log("產品載入成功:", response);
            return response.data || [];
        } else {
            throw new Error(response.error || "載入產品失敗");
        }
    } catch (error) {
        console.error("載入產品錯誤:", error);
        throw error;
    }
}

// 頁面載入時自動測試
document.addEventListener('DOMContentLoaded', function() {
    console.log("API修復腳本載入完成");
    
    // 如果頁面有測試按鈕，自動執行測試
    if (document.getElementById('test-api-btn')) {
        console.log("找到測試按鈕，自動執行API測試");
        testApiConnection();
    }
});

// 導出函數供其他腳本使用
window.fixedApiRequest = fixedApiRequest;
window.loadProductsFixed = loadProductsFixed;
window.testApiConnection = testApiConnection;
