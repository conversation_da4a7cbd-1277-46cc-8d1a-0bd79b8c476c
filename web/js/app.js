// API 基礎設定 - 使用配置檔案
const API_BASE = window.CONFIG ? window.CONFIG.API_BASE : "http://localhost:8080";
const DEBUG_MODE = window.CONFIG ? window.CONFIG.DEBUG : true;

console.log("API_BASE 設定為:", API_BASE);
console.log("當前環境:", window.location.hostname === 'localhost' ? '開發環境' : '生產環境');
console.log("DEBUG 模式:", DEBUG_MODE);
console.log("當前時間戳:", new Date().toISOString());

// 檢查並清除不同域名的舊快取（只在域名改變時清除）
const currentDomain = localStorage.getItem("current_domain");
if (currentDomain && currentDomain !== API_BASE) {
  console.log(`域名已改變 (${currentDomain} → ${API_BASE})，清除舊快取`);
  localStorage.clear();
}
localStorage.setItem("current_domain", API_BASE);

// 讀取認證資料並添加詳細日誌
let authToken = localStorage.getItem("authToken");
let tokenExpiry = localStorage.getItem("tokenExpiry");
let currentUser = null;

// 訊息記錄存儲
let messageHistory = JSON.parse(localStorage.getItem("messageHistory")) || [
  {
    id: 1,
    sent_at: new Date(Date.now() - 86400000).toISOString(),
    type: 'announcement',
    title: '系統維護通知',
    target_audience: 'all',
    send_methods: ['web'],
    status: 'sent'
  }
];

// 啟動時的認證狀態檢查
console.log("認證狀態:", authToken ? "已登入" : "未登入");

// Token 管理函數
function setAuthToken(token, expiresIn) {
  authToken = token;
  const expiryTime = Date.now() + expiresIn * 1000;
  tokenExpiry = expiryTime;

  localStorage.setItem("authToken", token);
  localStorage.setItem("tokenExpiry", expiryTime.toString());

  console.log(`Token 更新：有效期至 ${new Date(expiryTime).toLocaleString()}`);
}

function isTokenExpired() {
  if (!authToken || !tokenExpiry) {
    return true;
  }

  const now = Date.now();
  const expiry = parseInt(tokenExpiry);

  // 只在真正過期時才認為過期，不提前判斷
  return now >= expiry;
}

function isTokenNearExpiry() {
  if (!authToken || !tokenExpiry) {
    return false;
  }

  const now = Date.now();
  const expiry = parseInt(tokenExpiry);

  // 提前 10 分鐘認為即將過期，用於自動刷新
  return now >= expiry - 10 * 60 * 1000;
}

function isTokenValid() {
  return authToken && tokenExpiry && !isTokenExpired();
}

function clearAuthToken() {
  authToken = null;
  tokenExpiry = null;
  localStorage.removeItem("authToken");
  localStorage.removeItem("tokenExpiry");
  localStorage.removeItem("user");
  console.log('已清除認證資料');
}

// API 請求函數 - 增強版
async function apiRequest(url, options = {}) {
  const fullUrl = `${API_BASE}${url}`;
  console.log("發送 API 請求到:", fullUrl);

  // 如果正在刷新 token，等待完成
  if (window.isRefreshingToken) {
    await new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (!window.isRefreshingToken) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);
    });
  }

  const config = {
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
    ...options,
  };

  // 添加認證 token
  if (authToken && isTokenValid()) {
    config.headers["Authorization"] = `Bearer ${authToken}`;
  }

  try {
    const response = await fetch(fullUrl, config);

    if (response.status === 401) {
      console.log(`收到 401 錯誤，資源: ${url}`);

      // 如果是刷新 token 的請求，直接返回錯誤
      if (url.includes('/auth/refresh')) {
        throw new Error("認證 token 無效");
      }

      // 嘗試自動刷新 token
      if (authToken) {
        console.log("嘗試自動刷新 token...");
        const refreshSuccess = await attemptTokenRefresh();

        if (refreshSuccess && isTokenValid()) {
          console.log("刷新成功，重試原始請求");
          // 更新 Authorization header
          config.headers["Authorization"] = `Bearer ${authToken}`;

          // 重試原始請求
          const retryResponse = await fetch(fullUrl, config);
          const retryData = await retryResponse.json();

          if (retryResponse.ok) {
            return retryData;
          } else {
            throw new Error(retryData.error || `HTTP ${retryResponse.status}`);
          }
        } else {
          console.log("Token 刷新失敗，需要重新登入");
        }
      }

      // 刷新失敗或無 token，清除認證並跳轉到登入頁
      clearAuthToken();
      currentUser = null;
      showLogin();
      throw new Error("請重新登入");
    }

    // 檢查回應是否為空
    const responseText = await response.text();
    
    if (!responseText || responseText.trim() === '') {
      throw new Error("API回應為空");
    }

    // 嘗試解析JSON
    let data;
    try {
      data = JSON.parse(responseText);
    } catch (parseError) {
      console.error("JSON解析失敗:", parseError);
      console.error("原始回應:", responseText);
      
      // 如果是HTML錯誤頁面，提取有用資訊
      if (responseText.includes('<html') || responseText.includes('<!DOCTYPE')) {
        throw new Error("API返回了HTML頁面，可能是伺服器錯誤");
      }
      
      throw new Error(`API回應格式錯誤: ${parseError.message}`);
    }

    if (!response.ok) {
      throw new Error(data.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    return data;
  } catch (error) {
    // 網路連線錯誤處理
    if (error.name === "TypeError") {
      if (!authToken) {
        console.log("未登入狀態，網路錯誤");
        throw new Error("請先登入");
      } else {
        console.log("網路連線異常:", error.message);
        throw new Error("網路連線異常，請檢查網路連線");
      }
    }

    console.error(`API 請求失敗 (${url}):`, error.message);
    throw error;
  }
}

// DOM 元素 - 將在DOM載入後初始化
let loginSection, registerSection, mainContent, userInfo, loading, message;

// 自動 token 刷新機制
function setupAutomaticTokenRefresh() {
  // 每 5 分鐘檢查一次 token 狀態
  setInterval(async () => {
    if (!authToken || !isTokenValid()) {
      return;
    }

    if (isTokenNearExpiry()) {
      console.log("定期檢查：發現 token 即將過期，自動刷新...");
      const refreshSuccess = await attemptTokenRefresh();
      if (refreshSuccess) {
        console.log("定期刷新成功");
      } else {
        console.log("定期刷新失敗");
      }
    }
  }, 5 * 60 * 1000); // 5 分鐘
}

// 頁面隱藏/顯示事件處理
document.addEventListener('visibilitychange', async () => {
  if (!document.hidden && authToken) {
    console.log('頁面重新顯示，檢查 token 狀態');

    if (isTokenExpired()) {
      console.log('頁面返回時發現 token 已過期，嘗試刷新');
      const refreshSuccess = await attemptTokenRefresh();
      if (!refreshSuccess) {
        showMessage('登入已過期，請重新登入', 'warning');
        clearAuthToken();
        showLogin();
      }
    } else if (isTokenNearExpiry()) {
      console.log('頁面返回時發現 token 即將過期，預先刷新');
      await attemptTokenRefresh();
    }
  }
});

// 初始化應用程式
document.addEventListener("DOMContentLoaded", function () {
  console.log('DOM 已載入，開始初始化...');

  // 初始化DOM元素
  loginSection = document.getElementById("login-section");
  registerSection = document.getElementById("register-section");
  mainContent = document.getElementById("main-content");
  userInfo = document.getElementById("user-info");
  loading = document.getElementById("loading");
  message = document.getElementById("message");

  console.log('DOM元素初始化完成:', {
    loginSection: !!loginSection,
    registerSection: !!registerSection,
    mainContent: !!mainContent,
    userInfo: !!userInfo,
    loading: !!loading,
    message: !!message
  });

  setTimeout(async () => {
    await initializeApp();
    setupEventListeners();
    setupAutomaticTokenRefresh();

    // 設置管理員功能的事件監聽器
    if (typeof setupApprovalEventListeners === 'function') {
      setupApprovalEventListeners();
    }
    if (typeof setupProductManagementEventListeners === 'function') {
      setupProductManagementEventListeners();
    }

    console.log('=== 應用程式初始化完成 ===');
  }, 0);
});

// 初始化應用程式 - 增強版
async function initializeApp() {
  console.log("=== 開始初始化應用程式 ===");
  console.log("authToken:", authToken ? "存在" : "不存在");
  console.log("tokenExpiry:", tokenExpiry ? new Date(parseInt(tokenExpiry)).toLocaleString() : "不存在");

  // 先嘗試從 localStorage 恢復用戶資料
  const savedUser = localStorage.getItem("user");
  if (savedUser) {
    try {
      currentUser = JSON.parse(savedUser);
      console.log("從 localStorage 恢復用戶資料:", currentUser.username);
    } catch (error) {
      console.error("解析用戶資料失敗:", error);
      localStorage.removeItem("user");
    }
  }

  // 檢查 token 狀態
  if (!authToken) {
    console.log("沒有 token，顯示登入頁面");
    showLogin();
    return;
  }

  // Token 已完全過期
  if (isTokenExpired()) {
    console.log("Token 已過期，嘗試刷新...");
    const refreshSuccess = await attemptTokenRefresh();
    if (refreshSuccess && isTokenValid()) {
      console.log("Token 刷新成功，繼續初始化");
      await completeInitialization();
      return;
    } else {
      console.log("Token 刷新失敗，需要重新登入");
      clearAuthToken();
      showLogin();
      return;
    }
  }

  // Token 有效但即將過期，先刷新再繼續
  if (isTokenNearExpiry()) {
    console.log("Token 即將過期，預先刷新...");
    const refreshSuccess = await attemptTokenRefresh();
    if (!refreshSuccess) {
      console.warn("Token 預先刷新失敗，但當前 token 仍有效");
    }
  }

  // Token 有效，完成初始化
  console.log("Token 有效，完成應用初始化");
  await completeInitialization();
}

// 完成應用初始化
async function completeInitialization() {
  try {
    // 如果已有用戶資料，先顯示界面再更新
    if (currentUser && currentUser.username) {
      console.log("使用快取用戶資料快速顯示界面");
      updateUserInfo();
      updateUIBasedOnPermissions();
      showMainContent();

      // 在背景驗證和更新用戶資料
      try {
        await getCurrentUser();
        console.log("背景更新用戶資料成功");
      } catch (error) {
        console.warn("背景更新用戶資料失敗，使用快取資料:", error.message);
        // 如果是網路錯誤，不影響用戶體驗
        if (error.name === "TypeError" || error.message.includes('Network')) {
          showMessage("網路連線異常，使用離線模式", "warning");
        }
      }
    } else {
      // 沒有快取資料，需要從服務器獲取
      console.log("沒有快取資料，從服務器獲取用戶資料");
      await getCurrentUser();
      updateUserInfo();
      updateUIBasedOnPermissions();
      showMainContent();
    }
  } catch (error) {
    console.error("完成初始化時發生錯誤:", error);

    // 根據錯誤類型決定處理方式
    if (error.message.includes("401") || error.message.includes("請重新登入")) {
      console.log("認證失效，嘗試刷新 token...");
      const refreshSuccess = await attemptTokenRefresh();
      if (refreshSuccess) {
        try {
          await getCurrentUser();
          updateUserInfo();
          updateUIBasedOnPermissions();
          showMainContent();
          return;
        } catch (retryError) {
          console.error("刷新後仍然失敗:", retryError);
        }
      }
      // 刷新失敗，清除認證
      clearAuthToken();
      showLogin();
      showMessage("登入已過期，請重新登入", "warning");
    } else if (error.name === "TypeError" && currentUser) {
      // 網路錯誤但有快取資料，使用離線模式
      console.log("網路錯誤，使用快取資料離線模式");
      updateUserInfo();
      updateUIBasedOnPermissions();
      showMainContent();
      showMessage("網路連線異常，使用離線模式", "warning");
    } else {
      // 其他錯誤
      console.error("初始化失敗:", error);
      showMessage("應用初始化失敗: " + error.message, "error");
      clearAuthToken();
      showLogin();
    }
  }
}

// 設定事件監聽器
function setupEventListeners() {
  // 登入表單
  document.getElementById("login-form").addEventListener("submit", handleLogin);

  // 註冊表單
  document
    .getElementById("register-form")
    .addEventListener("submit", handleRegister);

  // 個人資料表單
  document
    .getElementById("profile-form")
    .addEventListener("submit", handleProfileUpdate);

  // 顯示註冊/登入表單
  document
    .getElementById("show-register")
    .addEventListener("click", showRegister);
  document.getElementById("show-login").addEventListener("click", showLogin);

  // 登出
  document.getElementById("logout-btn").addEventListener("click", logout);

  // 導航標籤 - 使用Bootstrap標籤頁事件
  document.querySelectorAll("#mainTabs .nav-link").forEach((tab) => {
    tab.addEventListener("click", () => {
      const tabName = tab.getAttribute('data-tab');
      if (tabName) {
        switchTab(tabName);
      }
    });
  });

  // 管理員標籤導航 - 使用Bootstrap標籤頁事件
  document.querySelectorAll("#adminSubTabs .nav-link").forEach((tab) => {
    tab.addEventListener("click", () => {
      const tabName = tab.getAttribute('data-tab');
      if (tabName) {
        switchAdminTab(tabName);
      }
    });
  });

  // 產品搜尋
  document
    .getElementById("search-btn")
    .addEventListener("click", searchProducts);
  document
    .getElementById("product-search")
    .addEventListener("keypress", function (e) {
      if (e.key === "Enter") {
        searchProducts();
      }
    });

  // 清除搜尋
  document
    .getElementById("clear-search-btn")
    .addEventListener("click", clearSearch);

  // 分頁控制
  document
    .getElementById("prev-page")
    .addEventListener("click", () => changePage(currentPage - 1));
  document
    .getElementById("next-page")
    .addEventListener("click", () => changePage(currentPage + 1));
  document
    .getElementById("goto-page")
    .addEventListener("click", gotoPage);
  document
    .getElementById("page-input")
    .addEventListener("keypress", function (e) {
      if (e.key === "Enter") {
        gotoPage();
      }
    });

  // 購物車操作
  document.getElementById("checkout-btn").addEventListener("click", checkout);
  document
    .getElementById("clear-cart-btn")
    .addEventListener("click", clearCart);

  // 訊息管理相關事件監聽器
  setupMessageEventListeners();
}

// 設定訊息管理事件監聽器
function setupMessageEventListeners() {
  console.log('設定訊息管理事件監聽器');

  // 使用事件委託來處理表單提交
  document.addEventListener('submit', function (e) {
    if (e.target && e.target.id === 'send-message-form') {
      console.log('表單提交事件被觸發');
      e.preventDefault();
      sendMessage();
    }

    // 範本表單提交
    if (e.target && e.target.id === 'template-form') {
      console.log('範本表單提交');
      e.preventDefault();
      saveTemplate();
    }
  });

  // 使用事件委託來處理按鈕點擊
  document.addEventListener('click', function (e) {
    if (e.target && e.target.id === 'preview-message-btn') {
      console.log('預覽按鈕被點擊');
      e.preventDefault();
      previewMessage();
    }
  });

  console.log('事件委託監聽器已設置');

  // 字數統計功能
  document.addEventListener('input', function (e) {
    if (e.target && e.target.id === 'message-content') {
      const content = e.target.value;
      const charCount = content.length;
      const counter = document.getElementById('char-count');
      if (counter) {
        counter.textContent = charCount;

        // 根據字數改變顏色和驗證狀態
        if (charCount > 500) {
          counter.style.color = '#e53e3e';
          e.target.classList.add('error');
          e.target.classList.remove('success');
        } else if (charCount > 400) {
          counter.style.color = '#dd6b20';
          e.target.classList.remove('error', 'success');
        } else if (charCount > 20) {
          counter.style.color = '#38a169';
          e.target.classList.add('success');
          e.target.classList.remove('error');
        } else {
          counter.style.color = '#718096';
          e.target.classList.remove('error', 'success');
        }
      }
    }
  });

  // 發送對象選擇變化 - 已移除此功能

  // 使用事件委託處理其他按鈕點擊
  document.addEventListener('click', function (e) {
    // 模態框關閉按鈕
    if (e.target && e.target.id === 'close-message-preview-modal') {
      const modal = document.getElementById("message-preview-modal");
      if (modal) modal.style.display = "none";
    }

    if (e.target && e.target.id === 'close-template-modal') {
      const modal = document.getElementById("template-modal");
      if (modal) modal.style.display = "none";
    }

    if (e.target && e.target.id === 'close-message-detail-modal') {
      const modal = document.getElementById("message-detail-modal");
      if (modal) modal.style.display = "none";
    }

    // 確認發送按鈕
    if (e.target && e.target.id === 'confirm-send-message') {
      const modal = document.getElementById("message-preview-modal");
      if (modal) modal.style.display = "none";
      sendMessage();
    }

    // 取消發送按鈕
    if (e.target && e.target.id === 'cancel-send-message') {
      const modal = document.getElementById("message-preview-modal");
      if (modal) modal.style.display = "none";
    }

    // 新增範本按鈕
    if (e.target && (e.target.id === 'create-template-btn' || e.target.closest('#create-template-btn'))) {
      console.log('新增範本按鈕被點擊');
      const modal = document.getElementById("template-modal");
      if (modal) {
        modal.style.display = "block";
        console.log('範本模態框已顯示');
      } else {
        console.error('找不到範本模態框');
      }
    }

    // 篩選按鈕
    if (e.target && e.target.id === 'filter-history-btn') {
      loadMessageHistory(); // 重新載入並應用篩選
    }
  });
}

// 顯示/隱藏函數
function showLogin() {
  loginSection.style.display = "block";
  registerSection.style.display = "none";
  mainContent.style.display = "none";
  userInfo.style.display = "none";
}

function showRegister() {
  loginSection.style.display = "none";
  registerSection.style.display = "block";
  mainContent.style.display = "none";
  userInfo.style.display = "none";
}

function showMainContent() {
  loginSection.style.display = "none";
  registerSection.style.display = "none";
  mainContent.style.display = "block";
  userInfo.style.display = "flex";

  // 預設載入產品頁面
  switchTab("products");
}

function showLoading() {
  if (loading) loading.style.display = "flex";
}

function hideLoading() {
  if (loading) loading.style.display = "none";
}

function showMessage(text, type = "success") {
  if (message) {
    message.textContent = text;
    message.className = `message ${type}`;
    message.style.display = "block";

    setTimeout(() => {
      message.style.display = "none";
    }, 3000);
  }
}

// 登入處理
async function handleLogin(e) {
  e.preventDefault();

  const formData = new FormData(e.target);
  const loginData = {
    username: formData.get("username"),
    password: formData.get("password"),
  };

  try {
    showLoading();

    // 基本驗證
    if (!loginData.username || !loginData.password) {
      throw new Error("請輸入用戶名和密碼");
    }

    console.log("嘗試登入:", loginData.username);

    // 調用真正的登入 API
    const response = await apiRequest('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(loginData),
    });

    if (!response.success) {
      throw new Error(response.error || "登入失敗");
    }

    // 從 API 響應中取得真正的 token 和用戶資料
    const { token, user, expires_in, permissions } = response.data;

    console.log("登入成功，用戶:", user.username);
    console.log("權限:", permissions);

    // 設定認證 token
    setAuthToken(token, expires_in);
    
    // 設定當前用戶資料
    currentUser = {
      ...user,
      permissions: permissions
    };

    localStorage.setItem("user", JSON.stringify(currentUser));

    showMessage("登入成功！", "success");
    showMainContent();

    // 根據用戶權限更新界面
    updateUIBasedOnPermissions();

  } catch (error) {
    console.error('登入錯誤:', error);
    showMessage(error.message || "登入失敗，請檢查用戶名和密碼", "error");
  } finally {
    hideLoading();
  }
}

// 註冊處理
async function handleRegister(e) {
  e.preventDefault();

  const formData = new FormData(e.target);
  const password = formData.get("password");
  const passwordConfirm = formData.get("password_confirm");

  // 密碼確認驗證
  if (password !== passwordConfirm) {
    showMessage("密碼與確認密碼不符", "error");
    return;
  }

  const registerData = {
    username: formData.get("username"),
    email: formData.get("email"),
    password: password,
    pharmacy_name: formData.get("pharmacy_name"),
    contact_person: formData.get("contact_person"),
    phone: formData.get("phone"),
    mobile: formData.get("mobile"),
    institution_code: formData.get("institution_code"),
    address: formData.get("address"),
  };

  try {
    showLoading();

    // 模擬註冊驗證
    if (!registerData.username || !registerData.password || !registerData.email) {
      throw new Error("請填寫必要的註冊資訊");
    }

    // 模擬 API 延遲
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 模擬註冊成功
    showMessage("註冊成功！請等待管理員審核後方可登入", "success");
    showLogin();

    // 自動填入用戶名
    const usernameInput = document.getElementById("username");
    if (usernameInput) {
      usernameInput.value = registerData.username;
    }

  } catch (error) {
    console.error('註冊錯誤:', error);
    showMessage(error.message, "error");
  } finally {
    hideLoading();
  }
}

// 處理個人資料更新
async function handleProfileUpdate(e) {
  e.preventDefault();

  const formData = new FormData(e.target);
  const profileData = {
    pharmacy_name: formData.get("pharmacy_name"),
    phone: formData.get("phone"),
    mobile: formData.get("mobile"),
    contact_person: formData.get("contact_person"),
    institution_code: formData.get("institution_code"),
    address: formData.get("address"),
  };

  try {
    showLoading();
    const response = await apiRequest("/api/auth/profile", {
      method: "PUT",
      body: JSON.stringify(profileData),
    });

    if (response.success) {
      // 更新本地用戶資料
      if (currentUser && response.data) {
        // 後端回傳結構 { success, data: { ...profile } }
        const profile = response.data;
        Object.assign(currentUser, profile);
        localStorage.setItem("user", JSON.stringify(currentUser));
      }

      showMessage("個人資料更新成功！");
      updateUserInfo(); // 更新顯示的用戶資訊
    } else {
      throw new Error(response.error || "個人資料更新失敗");
    }
  } catch (error) {
    showMessage(error.message, "error");
  } finally {
    hideLoading();
  }
}

// 載入個人資料到表單
function loadProfileData() {
  if (!currentUser) return;

  const form = document.getElementById("profile-form");
  if (!form) return;

  // 填入用戶資料
  const fields = [
    { id: "profile-username", value: currentUser.username },
    { id: "profile-email-addr", value: currentUser.email },
    { id: "profile-pharmacy-name", value: currentUser.pharmacy_name || "" },
    { id: "profile-phone", value: currentUser.phone || "" },
    { id: "profile-mobile", value: currentUser.mobile || "" },
    { id: "profile-contact-person", value: currentUser.contact_person || "" },
    { id: "profile-institution-code", value: currentUser.institution_code || "" },
    { id: "profile-address", value: currentUser.address || "" },
  ];

  fields.forEach(field => {
    const element = document.getElementById(field.id);
    if (element) {
      element.value = field.value;
    }
  });

  // 設定複選框
  // 通知選項已移除，不再設定 checkbox
}

// 嘗試刷新 token - 增強版
async function attemptTokenRefresh() {
  if (!authToken) {
    console.log('沒有 token，無法刷新');
    return false;
  }

  // 防止重複刷新
  if (window.isRefreshingToken) {
    console.log('正在刷新中，等待結果...');
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (!window.isRefreshingToken) {
          clearInterval(checkInterval);
          resolve(isTokenValid());
        }
      }, 100);
    });
  }

  window.isRefreshingToken = true;

  try {
    console.log("正在刷新 token...");

    // 使用 fetch 直接調用，避免 apiRequest 的循環依賴
    const response = await fetch(`${API_BASE}/api/auth/refresh`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${authToken}`
      },
      body: JSON.stringify({ token: authToken })
    });

    const data = await response.json();

    if (response.ok && data.success && data.data) {
      console.log("Token 刷新成功");
      setAuthToken(data.data.token, data.data.expires_in);

      // 更新用戶資料
      if (data.data.user) {
        currentUser = data.data.user;
        if (data.data.permissions) {
          currentUser.permissions = data.data.permissions;
        }
        localStorage.setItem("user", JSON.stringify(currentUser));
      }

      return true;
    } else {
      console.log("Token 刷新失敗:", data.error || '未知錯誤');
      return false;
    }
  } catch (error) {
    console.log("Token 刷新網路錯誤:", error.message);
    return false;
  } finally {
    window.isRefreshingToken = false;
  }
}

// 取得當前使用者資訊
async function getCurrentUser() {
  try {
    const response = await apiRequest("/api/auth/me");
    if (response.success) {
      // 保留現有的權限信息（如果有）
      const existingPermissions = currentUser?.permissions;
      currentUser = response.data;

      // 如果沒有權限信息，嘗試獲取權限
      if (!existingPermissions) {
        try {
          await getUserPermissions();
        } catch (permError) {
          console.warn("獲取權限失敗，使用預設權限:", permError);
          // 如果無法獲取權限，可以考慮基於 role_id 設置基本權限
        }
      } else {
        currentUser.permissions = existingPermissions;
      }

      // 更新 localStorage 中的用戶資料
      localStorage.setItem("user", JSON.stringify(currentUser));
      updateUserInfo();
      updateUIBasedOnPermissions();
    } else {
      throw new Error(response.error || "獲取用戶資料失敗");
    }
  } catch (error) {
    console.error("getCurrentUser 錯誤:", error);
    // 重新拋出錯誤，讓調用者處理
    throw error;
  }
}

// 取得使用者權限資訊
async function getUserPermissions() {
  try {
    console.log("獲取用戶權限");
    // 使用模擬權限數據
    if (currentUser) {
      // 根據用戶名和角色判斷權限
      if (currentUser.username === 'admin' || currentUser.pharmacy_name === 'admin') {
        currentUser.permissions = {
          role_name: 'admin',
          permissions: [
            'products.create', 'products.read', 'products.update', 'products.delete',
            'orders.create', 'orders.read', 'orders.update', 'orders.delete',
            'users.create', 'users.read', 'users.update', 'users.delete'
          ]
        };
      } else if (currentUser.username.includes('pharmacy') || currentUser.username.includes('藥局') || currentUser.role === 'pharmacy') {
        currentUser.permissions = {
          role_name: 'pharmacy',
          permissions: ['products.read', 'orders.create', 'orders.read', 'orders.update']
        };
      } else {
        // 一般用戶
        currentUser.permissions = {
          role_name: 'user',
          permissions: ['products.read', 'orders.create', 'orders.read']
        };
      }

      // 更新 localStorage 中的用戶資料
      localStorage.setItem("user", JSON.stringify(currentUser));
      console.log("用戶權限設置完成:", currentUser.permissions);
    }
  } catch (error) {
    console.error("獲取用戶權限失敗:", error);
    // 權限獲取失敗不應該影響登入狀態，使用預設權限
    if (!currentUser.permissions) {
      currentUser.permissions = { role_name: 'user', permissions: [] };
    }
  }
}

// 更新使用者資訊顯示
function updateUserInfo() {
  if (currentUser) {
    document.getElementById("user-display-name").textContent =
      currentUser.pharmacy_name || currentUser.username;
    userInfo.style.display = "flex";
  }
}

// 根據權限更新 UI 顯示
function updateUIBasedOnPermissions() {
  if (!currentUser || !currentUser.permissions) {
    return;
  }

  const permissions = currentUser.permissions;

  // 權限檢查：admin、pharmacy、其他用戶
  const isAdmin = permissions && permissions.role_name === 'admin';
  const isPharmacy = permissions && permissions.role_name === 'pharmacy';
  const isUser = permissions && permissions.role_name === 'user'; // 一般用戶

  console.log('用戶角色:', permissions.role_name,
    isAdmin ? '(管理員)' : isPharmacy ? '(藥局用戶)' : isUser ? '(一般用戶)' : '(未知角色)');
  console.log('角色判斷結果:', { isAdmin, isPharmacy, isUser });

  // 根據角色顯示不同的導航標籤
  updateNavigationTabs(isAdmin, isPharmacy, isUser);

  // 更新用戶信息顯示
  updateUserRoleDisplay(permissions, isAdmin, isPharmacy);

  // 根據權限調整功能訪問
  updateFeatureAccess(permissions, isAdmin, isPharmacy, isUser);
}

// 根據角色更新導航標籤顯示 - 簡化版：只有管理員和一般用戶
function updateNavigationTabs(isAdmin, isPharmacy, isUser) {
  console.log('updateNavigationTabs 被調用，參數:', { isAdmin, isPharmacy, isUser });

  // 獲取所有導航標籤
  const productTab = document.querySelector('[data-tab="products"]');
  const cartTab = document.querySelector('[data-tab="cart"]');
  const ordersTab = document.querySelector('[data-tab="orders"]');
  const profileTab = document.querySelector('[data-tab="profile"]');
  const promotionsTab = document.querySelector('[data-tab="promotions-view"]');
  const adminTab = document.querySelector('[data-tab="admin"]');

  console.log('找到的標籤元素:', {
    productTab: !!productTab,
    cartTab: !!cartTab,
    ordersTab: !!ordersTab,
    profileTab: !!profileTab,
    promotionsTab: !!promotionsTab,
    adminTab: !!adminTab
  });

  if (isAdmin) {
    // 管理員界面: 產品管理 + 訂單管理(所有) + 系統管理
    if (productTab) {
      productTab.style.display = "block";
      productTab.textContent = "📦 產品管理";
    }
    if (cartTab) cartTab.style.display = "none";  // 管理員不需要購物車
    if (ordersTab) {
      ordersTab.style.display = "block";
      ordersTab.textContent = "📋 訂單管理";
    }
    if (profileTab) profileTab.style.display = "none";  // 管理員不需要個人資料
    if (promotionsTab) promotionsTab.style.display = "none"; // 管理員從系統管理發訊息
    if (adminTab) {
      adminTab.style.display = "block";
      adminTab.textContent = "⚙️ 系統管理";
    }
  } else if (isPharmacy) {
    // 藥局用戶界面: 產品訂購 + 購物車 + 我的訂單 + 訊息
    if (productTab) {
      productTab.style.display = "block";
      productTab.textContent = "🛒 產品訂購";
    }
    if (cartTab) {
      cartTab.style.display = "block";
      cartTab.textContent = "🛒 購物車";
    }
    if (ordersTab) {
      ordersTab.style.display = "block";
      ordersTab.textContent = "📋 我的訂單";
    }
    if (profileTab) profileTab.style.display = "block";
    if (promotionsTab) {
      promotionsTab.style.display = "block";
      promotionsTab.textContent = "📢 訊息";
    }
    if (adminTab) adminTab.style.display = "none";  // 藥局用戶無系統管理權限
  } else {
    // 一般用戶界面
    console.log('設置一般用戶界面');
    if (productTab) {
      productTab.style.display = "block";
      productTab.textContent = "🛒 產品瀏覽";
    }
    if (cartTab) {
      cartTab.style.display = "block";
      cartTab.textContent = "🛒 購物車";
    }
    if (ordersTab) {
      ordersTab.style.display = "block";
      ordersTab.textContent = "📋 我的訂單";
    }
    if (profileTab) profileTab.style.display = "block";
    if (promotionsTab) {
      console.log('設置一般用戶的訊息標籤為可見');
      promotionsTab.style.display = "block";
      promotionsTab.textContent = "📢 訊息";
    }
    if (adminTab) adminTab.style.display = "none";  // 一般用戶無系統管理權限
  }
}

// 更新用戶角色顯示
function updateUserRoleDisplay(permissions, isAdmin, isPharmacy) {
  const userDisplayName = document.getElementById("user-display-name");
  if (userDisplayName && currentUser) {
    const roleText = isAdmin ? " (管理員)" : isPharmacy ? " (藥局)" : " (用戶)";
    userDisplayName.textContent = (currentUser.pharmacy_name || currentUser.username) + roleText;
  }
}

// 更新管理員標籤內容
function updateAdminTabContent(permissions) {
  const isAdmin = permissions.role_name === 'admin';

  if (isAdmin) {
    // 管理員可以看到所有訊息管理功能
    console.log('設定管理員訊息管理功能...');

    // 確保管理員標籤顯示
    const adminTab = document.querySelector('[data-tab="admin"]');
    if (adminTab) {
      adminTab.style.display = '';
      adminTab.style.visibility = 'visible';
    }

    // 載入訊息管理功能
    setTimeout(() => {
      loadMessageHistory();
      loadMessageTemplates();
    }, 100);
  } else {
    // 非管理員隱藏管理標籤，但可以查看促銷訊息
    const adminTab = document.querySelector('[data-tab="admin"]');
    if (adminTab) {
      adminTab.style.display = 'none';
      adminTab.style.visibility = 'hidden';
    }

    // 確保促銷訊息標籤可見
    const promotionsTab = document.querySelector('[data-tab="promotions-view"]');
    if (promotionsTab) {
      promotionsTab.style.display = '';
      promotionsTab.style.visibility = 'visible';
    }
  }
}

// 更新功能訪問權限
function updateFeatureAccess(permissions, isAdmin, isPharmacy, isUser) {
  // 更新產品表格標題和功能
  updateProductTableForRole(isAdmin, isPharmacy, isUser);

  console.log('用戶權限:', {
    role: permissions.role_name,
    isAdmin: isAdmin,
    permissions: permissions.permissions || []
  });
}

// 根據角色更新產品表格
function updateProductTableForRole(isAdmin, isPharmacy, isUser) {
  const productHeader = document.querySelector('.product-header h3');

  if (productHeader) {
    if (isAdmin) {
      productHeader.textContent = '📦 產品庫存管理';
    } else if (isPharmacy) {
      productHeader.textContent = '🛒 產品訂購目錄';
    } else {
      productHeader.textContent = '🛒 產品瀏覽目錄';
    }
  }
}

// 登出
function logout() {
  clearAuthToken();
  currentUser = null;
  showLogin();
  showMessage("已登出");
}

// 切換標籤
function switchTab(tabName) {
  // 檢查是否已登入
  if (!authToken || isTokenExpired()) {
    showLogin();
    return;
  }

  console.log('切換標籤到:', tabName);

  try {
    // 更新導航標籤
    document.querySelectorAll("#mainTabs .nav-link").forEach((tab) => {
      tab.classList.remove("active");
    });
    const targetTab = document.querySelector(`#${tabName}-tab`);
    if (targetTab) {
      targetTab.classList.add("active");
    }

    // 更新內容區域 - 使用Bootstrap標籤頁API
    const tab = new bootstrap.Tab(targetTab);
    tab.show();

    // 載入對應的資料
    switch (tabName) {
      case "products":
        loadProducts();
        break;
      case "cart":
        loadCart();
        break;
      case "orders":
        loadOrders({});
        break;
      case "profile":
        loadProfileData();
        break;
      case "promotions-view":
        loadPromotionMessages();
        break;
      case "admin":
        loadAdminContent();
        break;
    }
  } catch (error) {
    console.error('切換標籤時發生錯誤:', error);
    showMessage('切換標籤失敗: ' + error.message, 'error');
  }
}

// 切換管理員子標籤
function switchAdminTab(tabName) {
  console.log('切換管理員標籤到:', tabName);

  try {
    // 更新管理員導航標籤
    document.querySelectorAll("#adminSubTabs .nav-link").forEach((tab) => {
      tab.classList.remove("active");
    });
    const targetTab = document.querySelector(`#${tabName}-tab`);
    console.log('找到目標標籤:', !!targetTab);
    if (targetTab) {
      targetTab.classList.add("active");
    }

    // 更新管理員內容區域 - 使用Bootstrap標籤頁API
    const tab = new bootstrap.Tab(targetTab);
    tab.show();

    // 載入對應的管理員資料
    switch (tabName) {
      case "send-message":
        console.log('切換到發送訊息標籤');
        break;
      case "message-history":
        console.log('切換到訊息記錄標籤，載入數據');
        loadMessageHistory();
        break;
      case "message-templates":
        console.log('切換到訊息範本標籤，載入數據');
        loadMessageTemplates();
        break;
      case "user-approval":
        console.log('切換到用戶審核標籤，載入數據');
        loadPendingUsers();
        break;
      case "product-management":
        console.log('切換到產品管理標籤，載入數據');
        loadAdminProducts();
        break;
      default:
        console.warn('未知的標籤名稱:', tabName);
    }
  } catch (error) {
    console.error('切換標籤時發生錯誤:', error);
    showMessage('切換標籤失敗: ' + error.message, 'error');
  }
}

// 載入管理員內容
async function loadAdminContent() {
  // 預設載入發送訊息標籤（如果有權限）
  if (currentUser && currentUser.permissions && currentUser.permissions.role_name === 'admin') {
    switchAdminTab('send-message');
  } else {
    switchAdminTab('send-message');
  }
}

// 訊息發送管理相關函數

// 發送訊息
async function sendMessage() {
  console.log('sendMessage 函數被調用');

  const form = document.getElementById('send-message-form');
  if (!form) {
    console.error('找不到發送訊息表單');
    showMessage('系統錯誤：找不到表單', 'error');
    return;
  }

  const messageTypeEl = document.getElementById('message-type');
  const messageTitleEl = document.getElementById('message-title');
  const messageContentEl = document.getElementById('message-content');
  const sendWebEl = document.getElementById('send-web');
  const sendEmailEl = document.getElementById('send-email');
  const sendLineEl = document.getElementById('send-line');
  const scheduleSendEl = document.getElementById('schedule-send');

  console.log('表單元素檢查:', {
    messageType: !!messageTypeEl,
    messageTitle: !!messageTitleEl,
    messageContent: !!messageContentEl
  });

  if (!messageTypeEl || !messageTitleEl || !messageContentEl) {
    console.error('找不到必要的表單元素');
    showMessage('系統錯誤：找不到必要的表單元素', 'error');
    return;
  }

  const messageData = {
    type: messageTypeEl.value,
    title: messageTitleEl.value,
    content: messageContentEl.value,
    target_audience: 'all', // 預設發送給所有用戶
    send_methods: {
      web: sendWebEl ? sendWebEl.checked : true,
      email: sendEmailEl ? sendEmailEl.checked : false,
      line: sendLineEl ? sendLineEl.checked : false
    },
    schedule_send: scheduleSendEl ? scheduleSendEl.value : ''
  };

  // 驗證必填欄位
  if (!messageData.type || !messageData.title || !messageData.content) {
    showMessage('請填寫所有必填欄位', 'error');
    return;
  }

  // 檢查是否至少選擇一種發送方式
  if (!messageData.send_methods.web && !messageData.send_methods.email && !messageData.send_methods.line) {
    showMessage('請至少選擇一種發送方式', 'error');
    return;
  }

  // 獲取發送按鈕並設置載入狀態
  const submitBtn = form.querySelector('button[type="submit"]');
  const originalBtnText = submitBtn.innerHTML;

  try {
    showLoading();

    // 設置按鈕載入狀態
    if (submitBtn) {
      submitBtn.classList.add('loading');
      submitBtn.disabled = true;
    }

    // 模擬發送成功
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 創建新的訊息記錄
    const newMessage = {
      id: Date.now(), // 使用時間戳作為 ID
      sent_at: new Date().toISOString(),
      type: messageData.type,
      title: messageData.title,
      content: messageData.content,
      target_audience: messageData.target_audience,
      send_methods: Object.keys(messageData.send_methods).filter(key => messageData.send_methods[key]),
      status: 'sent'
    };

    // 將新訊息添加到記錄開頭
    messageHistory.unshift(newMessage);

    // 保存到 localStorage
    localStorage.setItem("messageHistory", JSON.stringify(messageHistory));

    console.log('新訊息已添加到記錄:', newMessage);
    console.log('更新後的訊息記錄總數:', messageHistory.length);
    console.log('localStorage 中的訊息記錄:', JSON.parse(localStorage.getItem("messageHistory")));

    showMessage('訊息發送成功', 'success');
    if (form) {
      form.reset();

      // 清除所有驗證狀態
      form.querySelectorAll('.form-control').forEach(field => {
        field.classList.remove('error', 'success');
      });

      // 清除錯誤訊息
      form.querySelectorAll('.error-message').forEach(error => {
        error.remove();
      });

      // 重置字數統計
      const counter = document.getElementById('char-count');
      if (counter) {
        counter.textContent = '0';
        counter.style.color = '#718096';
      }
    }
    // 切換到訊息記錄標籤
    setTimeout(() => {
      switchAdminTab('message-history');
    }, 500);
  } catch (error) {
    console.error('發送訊息錯誤:', error);
    showMessage('發送訊息失敗: ' + error.message, 'error');
  } finally {
    hideLoading();

    // 恢復按鈕狀態
    if (submitBtn) {
      submitBtn.classList.remove('loading');
      submitBtn.disabled = false;
      submitBtn.innerHTML = originalBtnText;
    }
  }
}

// 預覽訊息
function previewMessage() {
  const typeEl = document.getElementById('message-type');
  const titleEl = document.getElementById('message-title');
  const contentEl = document.getElementById('message-content');

  if (!typeEl || !titleEl || !contentEl) {
    console.error('找不到必要的表單元素');
    return;
  }

  const type = typeEl.value;
  const title = titleEl.value;
  const content = contentEl.value;

  if (!type || !title || !content) {
    showMessage('請填寫所有必填欄位後再預覽', 'error');
    return;
  }

  const typeLabels = {
    'promotion': '促銷活動',
    'announcement': '系統公告',
    'maintenance': '維護通知',
    'urgent': '緊急通知'
  };

  const audienceLabels = {
    'all': '所有用戶',
    'pharmacy': '藥局用戶',
    'admin': '管理員',
    'specific': '指定用戶'
  };

  const previewContent = `
    <div class="message-preview">
      <div class="preview-header">
        <span class="message-type-badge ${type}">${typeLabels[type]}</span>
        <h4>${title}</h4>
      </div>
      <div class="preview-content">
        <p>${content.replace(/\n/g, '<br>')}</p>
      </div>
      <div class="preview-footer">
        <small>發送對象: 所有用戶</small>
      </div>
    </div>
  `;

  document.getElementById('message-preview-content').innerHTML = previewContent;
  document.getElementById('message-preview-modal').style.display = 'block';
}

// 載入訊息發送記錄
async function loadMessageHistory() {
  console.log('載入訊息記錄');
  try {
    showLoading();

    // 從 localStorage 讀取最新的訊息記錄
    const storedMessages = JSON.parse(localStorage.getItem("messageHistory")) || [];
    console.log('從存儲中載入的訊息數量:', storedMessages.length);

    // 如果沒有存儲的訊息，使用預設的模擬數據
    const messages = storedMessages.length > 0 ? storedMessages : [
      {
        id: 1,
        sent_at: new Date(Date.now() - 86400000).toISOString(),
        type: 'announcement',
        title: '系統維護通知',
        target_audience: 'all',
        send_methods: ['web'],
        status: 'sent'
      }
    ];

    // 更新全局變量
    messageHistory = messages;

    renderMessageHistory(messages);
  } catch (error) {
    console.error('載入訊息記錄錯誤:', error);
    showMessage('載入訊息記錄失敗: ' + error.message, 'error');
  } finally {
    hideLoading();
  }
}

// 渲染訊息記錄表格
function renderMessageHistory(messages) {
  console.log('渲染訊息記錄，訊息數量:', messages.length);
  const tableBody = document.getElementById('message-history-body');
  console.log('找到表格主體元素:', !!tableBody);

  if (!tableBody) {
    console.error('找不到 message-history-body 元素');
    return;
  }

  if (messages.length === 0) {
    tableBody.innerHTML = '<tr><td colspan="7" class="no-data">沒有找到訊息記錄</td></tr>';
    return;
  }

  const typeLabels = {
    'promotion': '促銷活動',
    'announcement': '系統公告',
    'maintenance': '維護通知',
    'urgent': '緊急通知'
  };

  const audienceLabels = {
    'all': '所有用戶',
    'pharmacy': '藥局用戶',
    'admin': '管理員',
    'specific': '指定用戶'
  };

  const statusLabels = {
    'sent': '已發送',
    'pending': '待發送',
    'failed': '發送失敗'
  };

  tableBody.innerHTML = messages.map(message => `
    <tr>
      <td>${new Date(message.sent_at).toLocaleString()}</td>
      <td><span class="message-type-badge ${message.type}">${typeLabels[message.type]}</span></td>
      <td>${message.title}</td>
      <td>所有用戶</td>
      <td>${Array.isArray(message.send_methods) ? message.send_methods.join(', ') : message.send_methods}</td>
      <td><span class="status-badge ${message.status}">${statusLabels[message.status]}</span></td>
      <td>
        <button class="btn btn-secondary" onclick="viewMessageDetail(${message.id})">查看</button>
      </td>
    </tr>
  `).join('');
}

// 載入訊息範本
async function loadMessageTemplates() {
  console.log('載入訊息範本');
  try {
    showLoading();

    // 直接顯示模擬數據
    const templates = [
      {
        id: 1,
        name: '促銷活動範本',
        type: 'promotion',
        title: '限時優惠活動',
        content: '親愛的客戶，我們推出限時優惠活動，歡迎選購！'
      },
      {
        id: 2,
        name: '系統維護範本',
        type: 'maintenance',
        title: '系統維護通知',
        content: '系統將於 {date} 進行維護，維護期間可能無法正常使用，敬請見諒。'
      }
    ];

    renderMessageTemplates(templates);
  } catch (error) {
    console.error('載入範本錯誤:', error);
    showMessage('載入範本失敗: ' + error.message, 'error');
  } finally {
    hideLoading();
  }
}

// 渲染訊息範本
function renderMessageTemplates(templates) {
  const container = document.getElementById('templates-grid');
  if (!container) return;

  if (templates.length === 0) {
    container.innerHTML = '<p class="no-data">沒有找到範本</p>';
    return;
  }

  const typeLabels = {
    'promotion': '促銷活動',
    'announcement': '系統公告',
    'maintenance': '維護通知',
    'urgent': '緊急通知'
  };

  const typeIcons = {
    'promotion': '🎉',
    'announcement': '📢',
    'maintenance': '🔧',
    'urgent': '⚠️'
  };

  container.innerHTML = templates.map(template => `
    <div class="template-card" onclick="useTemplate(${template.id})" title="點擊使用此範本">
      <div class="template-header">
        <h5>
          <span class="template-icon">${typeIcons[template.type]}</span>
          ${template.name}
        </h5>
        <span class="template-type-badge ${template.type}">${typeLabels[template.type]}</span>
      </div>
      <div class="template-content">
        <h6>${template.title}</h6>
        <p>${template.content.substring(0, 120)}${template.content.length > 120 ? '...' : ''}</p>
      </div>
      <div class="template-actions" onclick="event.stopPropagation()">
        <button class="btn btn-primary" onclick="useTemplate(${template.id})">
          <span>🚀</span> 使用範本
        </button>
        <button class="btn btn-secondary" onclick="editTemplate(${template.id})">
          <span>✏️</span> 編輯
        </button>
        <button class="btn btn-danger" onclick="deleteTemplate(${template.id})">
          <span>🗑️</span> 刪除
        </button>
      </div>
    </div>
  `).join('');
}

// 使用範本
function useTemplate(templateId) {
  console.log('使用範本, ID:', templateId);

  // 模擬範本數據（實際應用中應該從 API 獲取）
  const templates = [
    {
      id: 1,
      name: '促銷活動範本',
      type: 'promotion',
      title: '限時優惠活動',
      content: '親愛的客戶，我們推出限時優惠活動，歡迎選購！\n\n活動詳情：\n• 全館商品 8 折優惠\n• 滿額免運費\n• 活動期間：即日起至月底\n\n立即選購，把握機會！'
    },
    {
      id: 2,
      name: '系統維護範本',
      type: 'maintenance',
      title: '系統維護通知',
      content: '親愛的用戶，\n\n系統將於 {date} 進行維護，維護期間可能無法正常使用，敬請見諒。\n\n維護時間：{time}\n預計影響：暫停服務約 2 小時\n\n如有緊急需求，請聯繫客服。\n謝謝您的配合！'
    }
  ];

  // 找到對應的範本
  const template = templates.find(t => t.id == templateId);

  if (!template) {
    showMessage('找不到指定的範本', 'error');
    return;
  }

  // 切換到發送訊息標籤
  switchAdminTab('send-message');

  // 等待標籤切換完成後填入表單
  setTimeout(() => {
    // 填入範本數據到表單
    const messageTypeEl = document.getElementById('message-type');
    const messageTitleEl = document.getElementById('message-title');
    const messageContentEl = document.getElementById('message-content');

    if (messageTypeEl) {
      messageTypeEl.value = template.type;
      // 觸發 change 事件以更新樣式
      messageTypeEl.dispatchEvent(new Event('change'));
    }

    if (messageTitleEl) {
      messageTitleEl.value = template.title;
      // 觸發 input 事件以進行驗證
      messageTitleEl.dispatchEvent(new Event('input'));
    }

    if (messageContentEl) {
      messageContentEl.value = template.content;
      // 觸發 input 事件以更新字數統計和驗證
      messageContentEl.dispatchEvent(new Event('input'));
    }

    // 顯示成功訊息
    showMessage(`✨ 已套用範本：${template.name}`, 'success');

    // 添加視覺反饋效果
    const filledFields = [messageTypeEl, messageTitleEl, messageContentEl].filter(el => el);
    filledFields.forEach((field, index) => {
      setTimeout(() => {
        field.style.transform = 'scale(1.02)';
        field.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.2)';
        setTimeout(() => {
          field.style.transform = '';
          field.style.boxShadow = '';
        }, 300);
      }, index * 100);
    });

    // 滾動到表單頂部
    const formWrapper = document.querySelector('.message-form-wrapper');
    if (formWrapper) {
      formWrapper.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, 300);
}

// 編輯範本
function editTemplate(templateId) {
  // 這裡應該打開範本編輯模態框
  showMessage('編輯範本功能開發中', 'info');
}

// 儲存範本
async function saveTemplate() {
  console.log('儲存範本函數被調用');

  const form = document.getElementById('template-form');
  if (!form) {
    console.error('找不到範本表單');
    return;
  }

  const templateData = {
    id: document.getElementById('template-id').value || Date.now(),
    name: document.getElementById('template-name').value.trim(),
    type: document.getElementById('template-type').value,
    title: document.getElementById('template-title').value.trim(),
    content: document.getElementById('template-content').value.trim()
  };

  // 驗證必填欄位
  if (!templateData.name || !templateData.type || !templateData.title || !templateData.content) {
    showMessage('請填寫所有必填欄位', 'error');
    return;
  }

  try {
    showLoading();

    // 模擬儲存成功
    await new Promise(resolve => setTimeout(resolve, 1000));

    showMessage('範本儲存成功！', 'success');

    // 關閉模態框
    const modal = document.getElementById('template-modal');
    if (modal) {
      modal.style.display = 'none';
    }

    // 清空表單
    form.reset();

    // 重新載入範本列表
    loadMessageTemplates();

  } catch (error) {
    console.error('儲存範本錯誤:', error);
    showMessage('儲存範本失敗: ' + error.message, 'error');
  } finally {
    hideLoading();
  }
}

// 刪除範本
async function deleteTemplate(templateId) {
  if (!confirm('確定要刪除這個範本嗎？')) {
    return;
  }

  showMessage('範本刪除成功！', 'success');
  loadMessageTemplates();
}

// 載入促銷訊息（一般用戶查看）
async function loadPromotionMessages() {
  console.log('載入促銷訊息');

  try {
    showLoading();

    // 從 localStorage 獲取管理員發送的訊息記錄
    const messageHistory = JSON.parse(localStorage.getItem("messageHistory")) || [];
    console.log('從 localStorage 讀取的訊息記錄:', messageHistory);

    // 如果沒有訊息記錄，創建一些測試訊息
    if (messageHistory.length === 0) {
      console.log('沒有找到訊息記錄，創建測試訊息');
      const testMessages = [
        {
          id: 1,
          sent_at: new Date(Date.now() - 86400000).toISOString(),
          type: 'announcement',
          title: '系統維護通知',
          content: '系統將於本週末進行維護，維護期間可能無法正常使用，敬請見諒。',
          target_audience: 'all',
          send_methods: ['web'],
          status: 'sent'
        },
        {
          id: 2,
          sent_at: new Date().toISOString(),
          type: 'promotion',
          title: '新品上市優惠',
          content: '新品上市，全館商品 8 折優惠，歡迎選購！',
          target_audience: 'all',
          send_methods: ['web'],
          status: 'sent'
        }
      ];
      localStorage.setItem("messageHistory", JSON.stringify(testMessages));
      messageHistory.push(...testMessages);
    }

    // 過濾出已發送的訊息，按時間排序（最新的在前）
    const sentMessages = messageHistory
      .filter(msg => msg.status === 'sent')
      .sort((a, b) => new Date(b.sent_at) - new Date(a.sent_at));

    console.log('過濾後的已發送訊息數量:', sentMessages.length);
    console.log('已發送訊息詳情:', sentMessages);

    renderPromotionMessages(sentMessages);

  } catch (error) {
    console.error('載入促銷訊息錯誤:', error);
    showMessage('載入訊息失敗: ' + error.message, 'error');
  } finally {
    hideLoading();
  }
}

// 渲染促銷訊息列表
function renderPromotionMessages(messages) {
  const container = document.getElementById('promotions-view-content');
  if (!container) {
    console.error('找不到促銷訊息容器');
    return;
  }

  if (messages.length === 0) {
    container.innerHTML = `
      <div class="no-messages">
        <div class="no-messages-icon">📭</div>
        <h3>暫無訊息</h3>
        <p>目前沒有新的促銷訊息或通知</p>
      </div>
    `;
    return;
  }

  const typeLabels = {
    'promotion': '促銷活動',
    'announcement': '系統公告',
    'maintenance': '維護通知',
    'urgent': '緊急通知'
  };

  const typeIcons = {
    'promotion': '🎉',
    'announcement': '📢',
    'maintenance': '🔧',
    'urgent': '⚠️'
  };

  const typeColors = {
    'promotion': '#e8f5e8',
    'announcement': '#e3f2fd',
    'maintenance': '#fff3e0',
    'urgent': '#ffebee'
  };

  container.innerHTML = messages.map(message => `
    <div class="promotion-message-card" data-type="${message.type}">
      <div class="message-header">
        <div class="message-type-info">
          <span class="message-icon">${typeIcons[message.type]}</span>
          <span class="message-type-label" style="background-color: ${typeColors[message.type]}">
            ${typeLabels[message.type]}
          </span>
        </div>
        <div class="message-date">
          ${new Date(message.sent_at).toLocaleDateString('zh-TW')}
        </div>
      </div>
      
      <div class="message-content">
        <h3 class="message-title">${message.title}</h3>
        <div class="message-text">
          ${message.content.replace(/\n/g, '<br>')}
        </div>
      </div>
      
      <div class="message-footer">
        <div class="message-methods">
          <span class="method-label">發送方式：</span>
          ${Array.isArray(message.send_methods) ?
      message.send_methods.map(method => {
        const methodLabels = { web: '網站通知', email: '電子郵件', line: 'Line通知' };
        return `<span class="method-tag">${methodLabels[method] || method}</span>`;
      }).join('') :
      `<span class="method-tag">${message.send_methods}</span>`
    }
        </div>
        <button class="btn btn-outline btn-sm" onclick="markAsRead(${message.id})">
          標記為已讀
        </button>
      </div>
    </div>
  `).join('');
}

// 標記訊息為已讀
function markAsRead(messageId) {
  console.log('標記訊息為已讀:', messageId);

  // 獲取已讀訊息列表
  let readMessages = JSON.parse(localStorage.getItem('readMessages')) || [];

  if (!readMessages.includes(messageId)) {
    readMessages.push(messageId);
    localStorage.setItem('readMessages', JSON.stringify(readMessages));

    // 更新 UI
    const messageCard = document.querySelector(`[onclick="markAsRead(${messageId})"]`);
    if (messageCard) {
      messageCard.textContent = '已讀';
      messageCard.disabled = true;
      messageCard.classList.add('disabled');
    }

    showMessage('已標記為已讀', 'success');
  }
}

// 查看訊息詳情
function viewMessageDetail(messageId) {
  console.log('查看訊息詳情, ID:', messageId);

  // 從訊息記錄中找到對應的訊息
  const message = messageHistory.find(msg => msg.id == messageId);

  if (!message) {
    showMessage('找不到指定的訊息', 'error');
    return;
  }

  const typeLabels = {
    'promotion': '促銷活動',
    'announcement': '系統公告',
    'maintenance': '維護通知',
    'urgent': '緊急通知'
  };

  const statusLabels = {
    'sent': '已發送',
    'pending': '待發送',
    'failed': '發送失敗'
  };

  const detailContent = `
    <div class="message-detail">
      <div class="detail-header">
        <h4>${message.title}</h4>
        <span class="message-type-badge ${message.type}">${typeLabels[message.type]}</span>
      </div>
      <div class="detail-info">
        <p><strong>發送時間：</strong>${new Date(message.sent_at).toLocaleString()}</p>
        <p><strong>發送對象：</strong>所有用戶</p>
        <p><strong>發送方式：</strong>${Array.isArray(message.send_methods) ? message.send_methods.join(', ') : message.send_methods}</p>
        <p><strong>狀態：</strong><span class="status-badge ${message.status}">${statusLabels[message.status]}</span></p>
      </div>
      <div class="detail-content">
        <h5>訊息內容：</h5>
        <div class="message-content-display">
          ${message.content ? message.content.replace(/\n/g, '<br>') : '無內容'}
        </div>
      </div>
    </div>
  `;

  const modal = document.getElementById('message-detail-modal');
  const contentDiv = document.getElementById('message-detail-content');

  if (modal && contentDiv) {
    contentDiv.innerHTML = detailContent;
    modal.style.display = 'block';
  } else {
    console.error('找不到訊息詳情模態框元素');
    showMessage('無法顯示訊息詳情', 'error');
  }
}

// 產品管理相關函數
let currentProducts = [];
let currentPage = 1;
let totalPages = 1;
const pageSize = 10;

// 從產品資料中獲取劑型
function getDosageForm(product) {
  // 如果已經有 dosage_form 值，直接返回
  if (product.dosage_form && product.dosage_form.trim() !== "") {
    return product.dosage_form;
  }

  // 根據產品名稱推測劑型
  const name = product.name || "";
  if (name.includes("錠") || name.toLowerCase().includes("tablet")) {
    return "錠劑";
  } else if (name.includes("膠囊") || name.toLowerCase().includes("capsule")) {
    return "膠囊";
  } else if (name.includes("糖漿") || name.toLowerCase().includes("syrup")) {
    return "糖漿";
  } else if (name.includes("注射") || name.toLowerCase().includes("injection")) {
    return "注射劑";
  } else if (name.includes("軟膏") || name.toLowerCase().includes("ointment")) {
    return "軟膏";
  } else if (name.includes("滴劑") || name.toLowerCase().includes("drop")) {
    return "滴劑";
  } else if (name.includes("粉") || name.toLowerCase().includes("powder")) {
    return "散劑";
  } else if (name.includes("液") || name.toLowerCase().includes("solution")) {
    return "溶液";
  }

  // 預設返回錠劑
  return "錠劑";
}

// 從產品資料中獲取成分含量
function getIngredients(product) {
  // 如果已經有 ingredients 值，直接返回
  if (product.ingredients && product.ingredients.trim() !== "") {
    return product.ingredients;
  }

  // 根據產品名稱推測成分含量
  const name = product.name || "";
  const lowerName = name.toLowerCase();

  // 常見藥品成分推測
  if (name.includes("普拿疼") || lowerName.includes("paracetamol")) {
    return "Paracetamol 500mg";
  } else if (name.includes("阿斯匹靈") || name.includes("阿司匹林") || lowerName.includes("aspirin")) {
    return "Acetylsalicylic Acid 100mg";
  } else if (name.includes("布洛芬") || lowerName.includes("ibuprofen")) {
    return "Ibuprofen 400mg";
  } else if (name.includes("安莫西林") || lowerName.includes("amoxicillin")) {
    return "Amoxicillin 250mg";
  } else if (name.includes("歐美拉唑") || lowerName.includes("omeprazole")) {
    return "Omeprazole 20mg";
  } else if (name.includes("二甲雙胍") || lowerName.includes("metformin")) {
    return "Metformin 500mg";
  } else if (name.includes("立普妥") || lowerName.includes("atorvastatin")) {
    return "Atorvastatin 10mg";
  } else if (name.includes("洛薩坦") || lowerName.includes("losartan")) {
    return "Losartan 50mg";
  } else if (name.includes("安普羅") || lowerName.includes("amlodipine")) {
    return "Amlodipine 5mg";
  }

  // 試著從產品名稱中提取劑量
  const dosageMatch = name.match(/(\d+)\s*mg/i);
  if (dosageMatch) {
    const dosage = dosageMatch[1];
    const nameWithoutDosage = name.replace(/\d+\s*mg/gi, "").trim();
    const firstWord = nameWithoutDosage.split(/\s+/)[0];
    return `${firstWord} ${dosage}mg`;
  }

  // 預設返回主要成分
  return "主要成分 100mg";
}

// 載入產品列表
async function loadProducts(page = 1) {
  try {
    showLoading();
    console.log(`載入第 ${page} 頁產品，每頁 ${pageSize} 筆`);

    const params = new URLSearchParams();
    params.append("page", page.toString());
    params.append("limit", pageSize.toString());

    const url = `/api/products?${params}`;
    console.log(`API請求URL: ${url}`);

    const response = await apiRequest(url);

    if (response.success) {
      currentProducts = response.data || [];
      currentPage = page;

      console.log(`載入了 ${currentProducts.length} 筆產品資料`);

      // 更新總頁數估算
      if (currentProducts.length < pageSize && page === 1) {
        totalPages = 1;
      } else if (currentProducts.length === pageSize) {
        // 如果當前頁滿了，估計還有下一頁
        totalPages = Math.max(page + 1, totalPages);
      } else if (currentProducts.length < pageSize) {
        // 如果當前頁未滿，則當前頁是最後一頁
        totalPages = page;
      }

      renderProductsTable();
      updatePaginationInfo();
      updatePaginationControls();
    } else {
      console.error("API響應錯誤:", response);
      throw new Error(response.error || "載入產品失敗");
    }
  } catch (error) {
    console.error("載入產品錯誤:", error);
    showMessage("載入產品失敗: " + error.message, "error");

    const gridBody = document.getElementById("products-grid-body");
    if (gridBody) {
      gridBody.innerHTML = '<div class="no-data" style="text-align: center; padding: 40px; color: #999;">載入產品失敗</div>';
    }
  } finally {
    hideLoading();
  }
}

// 渲染產品網格 - 新的卡片式佈局
function renderProductsTable() {
  const gridBody = document.getElementById("products-grid-body");
  if (!gridBody) return;

  if (currentProducts.length === 0) {
    gridBody.innerHTML = '<div class="no-data" style="text-align: center; padding: 40px; color: #999;">沒有找到產品</div>';
    return;
  }

  // 判斷是否為管理員
  const isAdmin = currentUser &&
    currentUser.permissions &&
    currentUser.permissions.role_name === 'admin';

  gridBody.innerHTML = currentProducts
    .map((product) => createProductCard(product, isAdmin))
    .join("");
}

// 創建產品卡片
function createProductCard(product, isAdmin) {
  const status = getProductStatus(product);
  const nhiLevel = getNhiLevel(product);

  return `
    <div class="product-card">
      <!-- 健保資訊欄 -->
      <div class="nhi-info">
        <div class="nhi-details">
          <div class="nhi-code">${product.nhi_code || "N/A"}</div>
          <div class="nhi-level">健保價: ${product.nhi_price || "N/A"}</div>
          <div class="nhi-expiry">2027.04.30</div>
        </div>
      </div>
      
      <!-- 品名/成分欄 -->
      <div class="product-name">
        <div class="product-title">${product.name || "N/A"}</div>
        <div class="product-ingredients">${getIngredients(product)}</div>
      </div>
      
      <!-- 規格欄 -->
      <div class="product-dosage">
        <div class="dosage-form">${product.dosage_form || "N/A"}</div>
      </div>
      
      <!-- 單價欄 -->
      <div class="product-price">
        <div class="price-value">${product.selling_price || product.unit_price || "0"}</div>
        <div class="price-unit">/元</div>
      </div>
      
      <!-- 數量欄 -->
      <div class="product-quantity">
        ${getQuantityInput(product, isAdmin)}
        <div class="quantity-label">${isAdmin ? '庫存' : '訂量'}</div>
      </div>
      
      <!-- 功能欄 -->
      <div class="product-actions">
        <button class="action-btn cart" title="加入購物車" onclick="addToCart(${product.id}, this)">🛒</button>
        <button class="action-btn favorite" title="收藏" onclick="toggleFavorite(${product.id})">❤️</button>
        ${getFunctionButton(product, isAdmin)}
      </div>
      
      <!-- 狀態欄 -->
      <div class="product-status">
        <span class="status-badge ${status.class}">${status.text}</span>
      </div>
    </div>
  `;
}

// 獲取產品狀態
function getProductStatus(product) {
  const stock = product.stock_quantity || 0;
  if (stock > 10) {
    return { class: 'in-stock', text: '供貨中' };
  } else if (stock > 0) {
    return { class: 'supply-interrupted', text: '供貨中' };
  } else {
    return { class: 'out-of-stock', text: '缺貨' };
  }
}

// 獲取健保層級
function getNhiLevel(product) {
  // 根據健保代碼或其他條件判斷層級
  if (product.nhi_code) {
    return Math.floor(Math.random() * 3) + 1; // 暫時隨機生成 1-3
  }
  return "N/A";
}

// 獲取數量輸入框
function getQuantityInput(product, isAdmin) {
  if (isAdmin) {
    return `<input type="number" class="quantity-input" min="0" value="${product.stock_quantity || 0}" onchange="updateStock(${product.id}, this.value)">`;
  } else {
    return `<input type="number" class="quantity-input" min="1" max="${product.stock_quantity || 0}" value="1" ${(product.stock_quantity || 0) <= 0 ? "disabled" : ""}>`;
  }
}

// 獲取功能按鈕
function getFunctionButton(product, isAdmin) {
  if (isAdmin) {
    return `
      <button class="function-btn edit-btn" onclick="editProduct(${product.id})" title="編輯產品">
        ✏️ 編輯
      </button>
    `;
  } else {
    return `
      <button class="function-btn favorite-btn" onclick="toggleFavorite(${product.id})" title="加入常選">
        ❤️ 常選
      </button>
    `;
  }
}

// 根據角色生成操作欄位
function getActionColumn(product, isAdmin) {
  if (isAdmin) {
    // 管理員看到庫存管理功能
    return `
      <td>
        <input type="number" 
               class="quantity-input"
               min="0" 
               value="${product.stock_quantity || 0}" 
               style="width: 60px;"
               onchange="updateStock(${product.id}, this.value)">
      </td>
      <td>
        <button class="btn btn-primary btn-sm" 
                onclick="editProduct(${product.id})"
                title="編輯產品">
            ✏️ 編輯
        </button>
      </td>
    `;
  } else {
    // 一般用戶看到購物車功能
    return `
      <td>
        <input type="number" 
               class="quantity-input"
               min="1" 
               max="${product.stock_quantity || 0}"
               value="1" 
               style="width: 60px;"
               ${(product.stock_quantity || 0) <= 0 ? "disabled" : ""}>
      </td>
      <td>
        <button class="btn-cart-icon" 
                onclick="addToCart(${product.id}, this)"
                ${(product.stock_quantity || 0) <= 0 ? "disabled" : ""}
                title="加入購物車">
            🛒 加入購物車
        </button>
      </td>
    `;
  }
}

// 管理員專用函數 - 更新庫存
async function updateStock(productId, newStock) {
  try {
    const response = await apiRequest(`/api/products/${productId}/stock`, {
      method: 'PUT',
      body: JSON.stringify({ stock_quantity: parseInt(newStock) })
    });

    if (response.success) {
      showMessage('庫存更新成功', 'success');
      // 更新本地數據
      const product = currentProducts.find(p => p.id === productId);
      if (product) {
        product.stock_quantity = parseInt(newStock);
      }
    } else {
      showMessage('庫存更新失敗: ' + response.error, 'error');
    }
  } catch (error) {
    showMessage('庫存更新失敗: ' + error.message, 'error');
  }
}

// 管理員專用函數 - 編輯產品
function editProduct(productId) {
  const product = currentProducts.find(p => p.id === productId);
  if (product) {
    showMessage('編輯產品功能開發中: ' + product.name, 'info');
    // TODO: 實現產品編輯功能
  }
}

// 新增功能函數
function downloadProduct(productId) {
  const product = currentProducts.find(p => p.id === productId);
  if (product) {
    showMessage('下載產品資訊: ' + product.name, 'info');
    // TODO: 實現產品資訊下載功能
  }
}

function toggleFavorite(productId) {
  const product = currentProducts.find(p => p.id === productId);
  if (product) {
    showMessage('收藏功能: ' + product.name, 'info');
    // TODO: 實現收藏功能
  }
}

function refreshProduct(productId) {
  const product = currentProducts.find(p => p.id === productId);
  if (product) {
    showMessage('重新整理產品: ' + product.name, 'info');
    // TODO: 實現單一產品重新整理功能
    loadProducts(); // 暫時重新載入所有產品
  }
}

// 更新分頁資訊顯示
function updatePaginationInfo() {
  const paginationInfo = document.getElementById("pagination-info");
  if (paginationInfo) {
    const start = (currentPage - 1) * pageSize + 1;
    const end = (currentPage - 1) * pageSize + currentProducts.length;
    const totalText = currentProducts.length < pageSize && currentPage === 1
      ? `${currentProducts.length} 筆`
      : `至少 ${end} 筆`;
    paginationInfo.textContent = `顯示第 ${start}-${end} 筆，共 ${totalText} 資料`;
  }
}

// 更新分頁控制按鈕
function updatePaginationControls() {
  const prevBtn = document.getElementById("prev-page");
  const nextBtn = document.getElementById("next-page");
  const pageInput = document.getElementById("page-input");

  if (prevBtn) {
    prevBtn.disabled = currentPage <= 1;
    prevBtn.style.opacity = currentPage <= 1 ? "0.5" : "1";
  }

  if (nextBtn) {
    const hasNextPage = currentProducts.length === pageSize;
    nextBtn.disabled = !hasNextPage;
    nextBtn.style.opacity = !hasNextPage ? "0.5" : "1";
  }

  if (pageInput) {
    pageInput.value = currentPage;
  }
}

// 切換頁面
async function changePage(newPage) {
  if (newPage < 1) return;
  if (newPage === currentPage) return;
  if (newPage > currentPage && currentProducts.length < pageSize) return; // 沒有下一頁

  await loadProducts(newPage);
}

// 前往指定頁面
async function gotoPage() {
  const pageInput = document.getElementById("page-input");
  const targetPage = parseInt(pageInput.value);

  if (isNaN(targetPage) || targetPage < 1) {
    showMessage("請輸入有效的頁數", "error");
    pageInput.value = currentPage;
    return;
  }

  await loadProducts(targetPage);
}

// 搜尋產品
async function searchProducts() {
  const searchTerm = document.getElementById("product-search").value.trim();

  try {
    showLoading();
    const params = new URLSearchParams();
    params.append("page", "1");
    params.append("limit", pageSize.toString());
    if (searchTerm) params.append("search", searchTerm);

    const response = await apiRequest(`/api/products?${params}`);

    if (response.success) {
      currentProducts = response.data || [];
      currentPage = 1;

      // 重設分頁狀態
      if (currentProducts.length === pageSize) {
        totalPages = 2; // 假設至少有下一頁
      } else {
        totalPages = 1;
      }

      renderProductsTable();
      updatePaginationInfo();
      updatePaginationControls();
    } else {
      throw new Error(response.error || "搜尋失敗");
    }
  } catch (error) {
    showMessage("搜尋失敗: " + error.message, "error");
  } finally {
    hideLoading();
  }
}

// 清除搜尋
async function clearSearch() {
  document.getElementById("product-search").value = "";
  currentPage = 1;
  await loadProducts(1);
}

// 加入購物車
async function addToCart(productId, button) {
  if (!authToken) {
    showMessage("請先登入才能加入購物車", "error");
    showLogin();
    return;
  }

  const container = button.closest('.product-card') || button.closest('tr');
  const quantityInput = container ? container.querySelector('.quantity-input') : null;

  if (!quantityInput) {
    showMessage("無法找到數量輸入框", "error");
    return;
  }

  const quantity = parseInt(quantityInput.value) || 1;

  try {
    showLoading();
    const response = await apiRequest("/api/cart", {
      method: "POST",
      body: JSON.stringify({
        product_id: productId,
        quantity: quantity,
      }),
    });

    if (response.success) {
      showMessage("✅ 已加入購物車！");
      quantityInput.value = 1; // 重置數量
    } else {
      throw new Error(response.error || "加入購物車失敗");
    }
  } catch (error) {
    showMessage(error.message, "error");
  } finally {
    hideLoading();
  }
}

// 載入購物車
async function loadCart() {
  // 檢查是否已登入
  if (!authToken || isTokenExpired()) {
    showLogin();
    return;
  }

  try {
    const response = await apiRequest("/api/cart");

    if (response.success) {
      const cart = response.data.cart;
      const cartContainer = document.getElementById("cart-items");
      const totalElement = document.getElementById("cart-total");

      if (cart && cart.items && cart.items.length > 0) {
        cartContainer.innerHTML = cart.items
          .map(
            (item) => `
                    <div class="cart-item">
                        <div class="cart-item-info">
                            <div class="cart-item-name">${item.product_name
              }</div>
                            <div class="cart-item-price">NT$ ${parseFloat(
                item.item.unit_price
              ).toFixed(2)} x ${item.item.quantity
              } = NT$ ${Math.round(parseFloat(item.item.subtotal))}</div>
                        </div>
                        <div class="cart-item-actions">
                            <button class="btn btn-secondary" onclick="updateCartItem(${item.item.id
              }, ${item.item.quantity - 1})" ${item.item.quantity <= 1 ? "disabled" : ""
              }>-</button>
                            <input type="number" class="quantity-input-cart" value="${item.item.quantity
              }" min="1" max="${item.available_stock
              }" onchange="updateCartItemFromInput(${item.item.id}, this.value)">
                            <button class="btn btn-secondary" onclick="updateCartItem(${item.item.id
              }, ${item.item.quantity + 1})" ${item.available_stock > item.item.quantity ? "" : "disabled"
              }>+</button>
                            <button class="btn btn-primary" onclick="updateCartItemByButton(${item.item.id}, this)">更新</button>
                            <button class="btn btn-danger" onclick="removeCartItem(${item.item.id
              })">移除</button>
                        </div>
                    </div>
                `
          )
          .join("");

        totalElement.textContent = Math.round(
          parseFloat(cart.total_amount || 0)
        );
      } else {
        cartContainer.innerHTML = "<p>購物車是空的</p>";
        totalElement.textContent = "0";
      }
    } else {
      const cartContainer = document.getElementById("cart-items");
      const totalElement = document.getElementById("cart-total");
      cartContainer.innerHTML = "<p>購物車是空的</p>";
      totalElement.textContent = "0";
    }
  } catch (error) {
    console.error("載入購物車錯誤:", error);
    showMessage("載入購物車失敗: " + error.message, "error");
  }
}

// 更新購物車項目
async function updateCartItem(itemId, newQuantity) {
  if (newQuantity <= 0) {
    removeCartItem(itemId);
    return;
  }

  try {
    showLoading();
    const response = await apiRequest(`/api/cart/items/${itemId}`, {
      method: "PUT",
      body: JSON.stringify({ quantity: newQuantity }),
    });

    if (response.success) {
      showMessage("✅ 數量已更新！");
      loadCart();
    } else {
      throw new Error(response.error || "更新失敗");
    }
  } catch (error) {
    console.error("更新購物車錯誤:", error);
    showMessage("更新購物車失敗: " + error.message, "error");
  } finally {
    hideLoading();
  }
}

// 從輸入框更新購物車項目數量
async function updateCartItemFromInput(itemId, newQuantity) {
  const quantity = parseInt(newQuantity);
  if (isNaN(quantity) || quantity <= 0) {
    showMessage("請輸入有效的數量", "error");
    loadCart();
    return;
  }

  await updateCartItem(itemId, quantity);
}

// 從「更新」按鈕觸發，使用目前輸入框的數量
function updateCartItemByButton(itemId, button) {
  const container = button.closest('.cart-item');
  const input = container ? container.querySelector('.quantity-input-cart') : null;
  if (!input) {
    showMessage('找不到數量輸入框', 'error');
    return;
  }
  const quantity = parseInt(input.value);
  if (isNaN(quantity) || quantity <= 0) {
    showMessage('請輸入有效的數量', 'error');
    return;
  }
  updateCartItem(itemId, quantity);
}

// 移除購物車項目
async function removeCartItem(itemId) {
  try {
    await apiRequest(`/api/cart/items/${itemId}`, {
      method: "DELETE",
    });

    showMessage("已移除商品");
    loadCart();
  } catch (error) {
    showMessage("移除商品失敗: " + error.message, "error");
  }
}

// 結帳
async function checkout() {
  try {
    showLoading();

    const notes = document.getElementById("order-notes").value.trim();

    const response = await apiRequest("/api/orders/cart", {
      method: "POST",
      body: JSON.stringify({
        notes: notes || null,
      }),
    });

    if (response.success) {
      showMessage("訂單建立成功！");
      document.getElementById("order-notes").value = "";
      loadCart();
      loadOrders({});
      switchTab("orders");
    } else {
      throw new Error(response.error || "結帳失敗");
    }
  } catch (error) {
    showMessage(error.message, "error");
  } finally {
    hideLoading();
  }
}

// 清空購物車
async function clearCart() {
  if (!confirm("確定要清空購物車嗎？")) {
    return;
  }

  try {
    await apiRequest("/api/cart/clear", {
      method: "DELETE",
    });

    showMessage("購物車已清空");
    loadCart();
  } catch (error) {
    showMessage("清空購物車失敗: " + error.message, "error");
  }
}

// 載入訂單 - 根據用戶角色載入不同的訂單資料
async function loadOrders(filters = {}) {
  // 檢查是否已登入
  if (!authToken || isTokenExpired()) {
    showLogin();
    return;
  }

  // 判斷是否為管理員
  console.log('loadOrders - currentUser:', currentUser);
  const isAdmin = currentUser &&
    currentUser.permissions &&
    currentUser.permissions.role_name === 'admin';
  console.log('loadOrders - isAdmin:', isAdmin);

  // 管理員預設篩選前5天的訂單
  if (isAdmin && Object.keys(filters).length === 0) {
    const today = new Date();
    const fiveDaysAgo = new Date(today);
    fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5);

    filters = {
      start_date: fiveDaysAgo.toISOString().split('T')[0] + 'T00:00:00Z',
      end_date: today.toISOString().split('T')[0] + 'T23:59:59Z'
    };

    console.log('管理員預設日期篩選:', filters);
  }

  // 更新界面標題和控制項
  updateOrdersInterface(isAdmin);

  try {
    showLoading();

    // 構建查詢參數
    const queryParams = new URLSearchParams();
    if (filters.status) queryParams.append('status', filters.status);
    if (filters.start_date) queryParams.append('start_date', filters.start_date);
    if (filters.end_date) queryParams.append('end_date', filters.end_date);

    // 管理員查看所有訂單，一般用戶只查看自己的訂單
    const apiEndpoint = isAdmin ? "/api/orders/all" : "/api/orders";
    const url = queryParams.toString() ? `${apiEndpoint}?${queryParams}` : apiEndpoint;
    const response = await apiRequest(url);

    if (response.success) {
      const orders = response.data?.orders || [];
      const ordersContainer = document.getElementById("orders-list");

      console.log("載入訂單資料:", orders);
      console.log("訂單容器元素:", ordersContainer);

      // 如果是管理員，顯示統計信息
      if (isAdmin) {
        updateOrderStats(orders);
      }

      if (orders.length > 0) {
        ordersContainer.innerHTML = orders
          .map((order) => renderOrderItem(order, isAdmin))
          .join("");
      } else {
        ordersContainer.innerHTML = "<p>沒有訂單記錄</p>";
      }
    } else {
      throw new Error(response.error || "載入訂單失敗");
    }
  } catch (error) {
    console.error("載入訂單錯誤:", error);
    showMessage("載入訂單失敗: " + error.message, "error");

    const ordersContainer = document.getElementById("orders-list");
    if (ordersContainer) {
      ordersContainer.innerHTML = "<p>載入訂單失敗</p>";
    }
  } finally {
    hideLoading();
  }
}

// 更新訂單界面（管理員 vs 一般用戶）
function updateOrdersInterface(isAdmin) {
  const ordersTitle = document.getElementById("orders-title");
  const adminControls = document.getElementById("admin-order-controls");
  const batchOperations = document.getElementById("batch-operations");

  if (isAdmin) {
    ordersTitle.textContent = "訂單管理（管理員）";
    adminControls.style.display = "block";
    batchOperations.style.display = "block";

    // 綁定篩選事件（只綁定一次）
    if (!adminControls.dataset.bound) {
      setupAdminOrderFilters();
      setupBatchOperations();
      adminControls.dataset.bound = "true";
    }
  } else {
    ordersTitle.textContent = "訂單歷史";
    adminControls.style.display = "none";
    batchOperations.style.display = "none";
  }
}

// 設置管理員訂單篩選功能
function setupAdminOrderFilters() {
  const applyFiltersBtn = document.getElementById("apply-filters");
  const clearFiltersBtn = document.getElementById("clear-filters");

  applyFiltersBtn.addEventListener("click", () => {
    const filters = {
      status: document.getElementById("status-filter").value
    };

    // 移除空值
    Object.keys(filters).forEach(key => {
      if (!filters[key]) delete filters[key];
    });

    loadOrders(filters);
  });

  clearFiltersBtn.addEventListener("click", () => {
    document.getElementById("status-filter").value = "";
    loadOrders({}); // 重新載入預設篩選（今天和昨天）
  });
}

// 更新訂單統計信息
function updateOrderStats(orders) {
  const statsContainer = document.getElementById("order-stats");

  const stats = {
    total: orders.length,
    pending: orders.filter(o => o.status === '待處理').length,
    processing: orders.filter(o => o.status === '揀貨中').length,
    completed: orders.filter(o => o.status === '已出貨').length
  };

  const totalAmount = orders.reduce((sum, order) => sum + parseFloat(order.total_amount || 0), 0);

  statsContainer.innerHTML = `
    <div class="stat-item">
      <span class="stat-number">${stats.total}</span>
      <span class="stat-label">總訂單</span>
    </div>
    <div class="stat-item">
      <span class="stat-number">${stats.pending}</span>
      <span class="stat-label">待處理</span>
    </div>
    <div class="stat-item">
      <span class="stat-number">${stats.processing}</span>
      <span class="stat-label">揀貨中</span>
    </div>
    <div class="stat-item">
      <span class="stat-number">${stats.completed}</span>
      <span class="stat-label">已出貨</span>
    </div>
    <div class="stat-item">
      <span class="stat-number">NT$ ${Math.round(totalAmount)}</span>
      <span class="stat-label">總金額</span>
    </div>
  `;
}

// 渲染單個訂單項目
function renderOrderItem(order, isAdmin) {
  // 狀態樣式映射 (直接使用後端返回的中文狀態)
  const statusClassMap = {
    "待處理": "status-pending",
    "揀貨中": "status-processing",
    "已出貨": "status-shipped"
  };

  const statusInfo = {
    text: order.status,
    class: statusClassMap[order.status] || "status-pending"
  };

  const checkbox = isAdmin ? `<input type="checkbox" class="order-checkbox" data-order-id="${order.id}">` : '';

  const adminActions = ''; // 移除狀態按鈕

  return `
    <div class="order-item ${isAdmin ? 'admin-order-item' : ''}" data-order-id="${order.id}">
      <div class="order-header">
        ${checkbox}
        <div class="order-number">訂單編號: 
          <a href="#" class="order-number-link" onclick="showOrderDetail(${order.id}); return false;">
            ${order.order_number}
          </a>
        </div>
        <div class="order-status">
          <span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>
        </div>
        <div class="order-total">NT$ ${Math.round(parseFloat(order.total_amount))}</div>
      </div>
      ${isAdmin && (order.pharmacy_name || order.username) ?
      `<div class="order-user">
          <strong>客戶：</strong>
          ${order.pharmacy_name || order.username}
          ${order.username && order.pharmacy_name !== order.username ? ` (${order.username})` : ''}
        </div>` : ''
    }
      <div class="order-date">下單時間: ${new Date(order.created_at).toLocaleDateString("zh-TW")}</div>
      ${order.notes ? `<div class="order-notes">備註: ${order.notes}</div>` : ""}
      ${adminActions}
    </div>
  `;
}

// 管理員更改訂單狀態
async function changeOrderStatus(orderId, newStatus) {
  try {
    const response = await apiRequest(`/api/orders/${orderId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status: newStatus })
    });

    if (response.success) {
      const statusText = getStatusText(newStatus);
      showMessage(`訂單狀態已更新為：${statusText}`, "success");
      loadOrders({}); // 重新載入訂單列表
    } else {
      throw new Error(response.error || "更新訂單狀態失敗");
    }
  } catch (error) {
    console.error("更新訂單狀態錯誤:", error);
    showMessage("更新訂單狀態失敗: " + error.message, "error");
  }
}

// 更新訂單狀態（從詳情頁面）
async function updateOrderStatus(orderId, newStatus) {
  try {
    const response = await apiRequest(`/api/orders/${orderId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status: newStatus })
    });

    if (response.success) {
      showMessage(`訂單狀態已更新為: ${newStatus}`, 'success');
      // 重新載入訂單列表（如果在訂單管理頁面）
      if (document.getElementById('orders-list')) {
        loadOrders({});
      }
    } else {
      throw new Error(response.error || '更新訂單狀態失敗');
    }
  } catch (error) {
    console.error('更新訂單狀態錯誤:', error);
    showMessage('更新訂單狀態失敗: ' + error.message, 'error');
    // 恢復選項到原來的狀態
    const select = document.getElementById('order-status-select');
    if (select) {
      // 重新載入訂單詳情來恢復正確狀態
      showOrderDetail(orderId);
    }
  }
}

// 更新訂單項目缺貨狀態
async function updateItemStockStatus(itemId, isStockOut) {
  try {
    const response = await apiRequest(`/api/order-items/${itemId}/stock-status`, {
      method: 'PUT',
      body: JSON.stringify({ is_stock_out: isStockOut })
    });

    if (response.success) {
      showMessage(`產品缺貨狀態已${isStockOut ? '設為缺貨' : '取消缺貨'}`, 'success');
    } else {
      throw new Error(response.error || '更新缺貨狀態失敗');
    }
  } catch (error) {
    console.error('更新缺貨狀態錯誤:', error);
    showMessage('更新缺貨狀態失敗: ' + error.message, 'error');
    // 恢復checkbox狀態
    const checkbox = document.getElementById(`stock-out-${itemId}`);
    if (checkbox) {
      checkbox.checked = !isStockOut;
    }
  }
}

// 更新訂單詳情（批量更新，包含狀態和缺貨狀態）
async function updateOrderDetails(orderId) {
  try {
    showMessage('正在更新訂單...', 'info');

    // 獲取當前訂單狀態
    const statusSelect = document.getElementById('order-status-select');
    let hasUpdates = false;

    // 更新訂單狀態（如果有變更）
    if (statusSelect) {
      const newStatus = statusSelect.value;
      // 這裡我們不需要檢查是否有變更，因為用戶點擊更新按鈕就是想要保存當前狀態
      await updateOrderStatus(orderId, newStatus);
      hasUpdates = true;
    }

    // 獲取所有缺貨狀態並批量更新
    const stockCheckboxes = document.querySelectorAll('input[id^="stock-out-"]');
    const stockStatusPromises = [];
    
    stockCheckboxes.forEach(checkbox => {
      const itemId = parseInt(checkbox.id.replace('stock-out-', ''));
      const isStockOut = checkbox.checked;
      stockStatusPromises.push(updateItemStockStatus(itemId, isStockOut));
    });

    if (stockStatusPromises.length > 0) {
      await Promise.all(stockStatusPromises);
      hasUpdates = true;
    }

    if (hasUpdates) {
      showMessage('訂單更新成功', 'success');
      // 延遲重新載入訂單詳情，讓用戶看到成功訊息
      setTimeout(() => {
        displayOrderDetailModal(orderId);
      }, 1500);
    } else {
      showMessage('沒有需要更新的變更', 'info');
    }

  } catch (error) {
    console.error('更新訂單詳情失敗:', error);
    showMessage('更新訂單詳情失敗: ' + error.message, 'error');
  }
}

// 顯示訂單詳情
async function showOrderDetail(orderId) {
  if (!authToken || isTokenExpired()) {
    showLogin();
    return;
  }

  try {
    showLoading();
    const response = await apiRequest(`/api/orders/${orderId}`);

    if (response.success && response.data.order) {
      const orderDetail = response.data.order;
      displayOrderDetailModal(orderDetail);
    } else {
      throw new Error(response.message || "載入訂單詳情失敗");
    }
  } catch (error) {
    console.error("載入訂單詳情錯誤:", error);
    showMessage("載入訂單詳情失敗: " + error.message, "error");
  } finally {
    hideLoading();
  }
}

// 顯示訂單詳情模態框
function displayOrderDetailModal(orderDetail) {
  const modal = document.getElementById("order-detail-modal");
  const content = document.getElementById("order-detail-content");
  const title = document.getElementById("order-detail-title");

  if (!modal || !content || !title) {
    console.error("找不到訂單詳情模態框元素");
    return;
  }

  // 設置標題
  title.textContent = `訂單詳情 - ${orderDetail.order.order_number}`;

  // 直接使用後端返回的中文狀態
  const statusText = orderDetail.order.status;
  const statusClassMap = {
    "待處理": "pending",
    "揀貨中": "processing",
    "已出貨": "shipped"
  };
  const statusClass = statusClassMap[orderDetail.order.status] || "pending";

  // 生成訂單詳情HTML
  content.innerHTML = `
    <div class="order-detail-header">
      <div class="order-detail-info">
        <div>
          <label>訂單編號:</label>
          <span>${orderDetail.order.order_number}</span>
        </div>
        <div>
          <label>訂單狀態:</label>
          ${currentUser && currentUser.permissions && currentUser.permissions.role_name === 'admin' ? 
            `<select id="order-status-select">
              <option value="待處理" ${orderDetail.order.status === '待處理' ? 'selected' : ''}>待處理</option>
              <option value="揀貨中" ${orderDetail.order.status === '揀貨中' ? 'selected' : ''}>揀貨中</option>
              <option value="已出貨" ${orderDetail.order.status === '已出貨' ? 'selected' : ''}>已出貨</option>
            </select>` :
            `<span class="order-status-badge ${statusClass}">${orderDetail.order.status}</span>`
          }
        </div>
        <div>
          <label>下單時間:</label>
          <span>${new Date(orderDetail.order.created_at).toLocaleDateString("zh-TW")}</span>
        </div>
        <div>
          <label>更新時間:</label>
          <span>${new Date(orderDetail.order.updated_at).toLocaleDateString("zh-TW")}</span>
        </div>
        ${orderDetail.order.notes ? `
        <div style="grid-column: 1 / -1;">
          <label>訂單備註:</label>
          <span>${orderDetail.order.notes}</span>
        </div>
        ` : ''}
      </div>
    </div>

    <div class="order-items-section">
      <h4>購買明細</h4>
      <table class="order-items-table">
        <thead>
          <tr>
            <th>藥品代碼</th>
            <th>產品名稱</th>
            <th>單價</th>
            <th>數量</th>
            <th>是否缺貨</th>
            <th>小計</th>
          </tr>
        </thead>
        <tbody>
          ${orderDetail.items.map(itemDetail => `
            <tr>
              <td class="product-code">${itemDetail.product_nhi_code || 'N/A'}</td>
              <td>${itemDetail.product_name}</td>
              <td class="price">NT$ ${parseFloat(itemDetail.item.unit_price).toFixed(2)}</td>
              <td class="quantity">${itemDetail.item.quantity}</td>
              <td class="stock-status">
                ${currentUser && currentUser.permissions && currentUser.permissions.role_name === 'admin' ? 
                  `<input type="checkbox" 
                         id="stock-out-${itemDetail.item.id}" 
                         ${itemDetail.item.is_stock_out ? 'checked' : ''}>
                   <label for="stock-out-${itemDetail.item.id}">缺貨</label>` :
                  `<span class="${itemDetail.item.is_stock_out ? 'text-danger' : 'text-muted'}">${itemDetail.item.is_stock_out ? '缺貨' : '正常'}</span>`
                }
              </td>
              <td class="price">NT$ ${Math.round(parseFloat(itemDetail.item.subtotal))}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </div>

    <div class="order-total-summary">
      <div>總項目數: ${orderDetail.items.length} 項</div>
      <div class="order-total-amount">訂單總額: NT$ ${Math.round(parseFloat(orderDetail.order.total_amount))}</div>
    </div>

    ${currentUser && currentUser.permissions && currentUser.permissions.role_name === 'admin' ? 
      `<div class="order-actions-spread">
        <button type="button" class="update-btn-large" onclick="updateOrderDetails(${orderDetail.order.id})">
          <i class="fas fa-save"></i> 更新訂單
        </button>
        <button type="button" class="print-btn-large" onclick="printOrder(${orderDetail.order.id})">
          <i class="fas fa-print"></i> 列印訂單
        </button>
      </div>` : ''
    }
  `;

  // 顯示模態框
  modal.style.display = "flex";

  // 設置關閉事件
  setupOrderDetailModalEvents();
}

// 設置訂單詳情模態框事件
function setupOrderDetailModalEvents() {
  const modal = document.getElementById("order-detail-modal");
  const closeBtn = document.getElementById("close-order-detail-modal");

  if (!modal || !closeBtn) {
    return;
  }

  // 點擊關閉按鈕
  closeBtn.onclick = function () {
    modal.style.display = "none";
  };

  // 點擊模態框外部關閉
  modal.onclick = function (event) {
    if (event.target === modal) {
      modal.style.display = "none";
    }
  };

  // ESC 鍵關閉
  document.onkeydown = function (event) {
    if (event.key === "Escape" && modal.style.display === "flex") {
      modal.style.display = "none";
    }
  };
}
// 設置批次操作功能
function setupBatchOperations() {
  const selectAllBtn = document.getElementById("select-all-orders");
  const deselectAllBtn = document.getElementById("deselect-all-orders");
  const applyBatchBtn = document.getElementById("apply-batch-status");
  const selectedCountSpan = document.getElementById("selected-count");

  // 全選功能
  selectAllBtn.addEventListener("click", () => {
    const checkboxes = document.querySelectorAll(".order-checkbox");
    checkboxes.forEach(checkbox => {
      checkbox.checked = true;
      checkbox.closest('.order-item').classList.add('selected');
    });
    updateSelectedCount();
  });

  // 取消全選功能
  deselectAllBtn.addEventListener("click", () => {
    const checkboxes = document.querySelectorAll(".order-checkbox");
    checkboxes.forEach(checkbox => {
      checkbox.checked = false;
      checkbox.closest('.order-item').classList.remove('selected');
    });
    updateSelectedCount();
  });

  // 批次更新狀態
  applyBatchBtn.addEventListener("click", async () => {
    const selectedCheckboxes = document.querySelectorAll(".order-checkbox:checked");
    const batchStatus = document.getElementById("batch-status-select").value;

    if (selectedCheckboxes.length === 0) {
      showMessage("請先選擇要更新的訂單", "warning");
      return;
    }

    if (!batchStatus) {
      showMessage("請選擇要更新的狀態", "warning");
      return;
    }

    const orderIds = Array.from(selectedCheckboxes).map(cb => cb.dataset.orderId);

    if (confirm(`確定要將 ${orderIds.length} 個訂單的狀態更新為 ${getStatusText(batchStatus)} 嗎？`)) {
      await batchUpdateOrderStatus(orderIds, batchStatus);
    }
  });

  // 監聽訂單項目點擊事件
  document.addEventListener('change', (e) => {
    if (e.target.classList.contains('order-checkbox')) {
      const orderItem = e.target.closest('.order-item');
      if (e.target.checked) {
        orderItem.classList.add('selected');
      } else {
        orderItem.classList.remove('selected');
      }
      updateSelectedCount();
    }
  });
}

// 更新選中數量顯示
function updateSelectedCount() {
  const selectedCheckboxes = document.querySelectorAll(".order-checkbox:checked");
  const selectedCountSpan = document.getElementById("selected-count");
  selectedCountSpan.textContent = `已選擇 ${selectedCheckboxes.length} 個訂單`;
}

// 獲取狀態中文文本 (現在直接返回，因為後端已經使用中文)
function getStatusText(status) {
  return status;
}

// 批次更新訂單狀態
async function batchUpdateOrderStatus(orderIds, newStatus) {
  try {
    showLoading();
    let successCount = 0;
    let failCount = 0;

    // 逐個更新訂單狀態
    for (const orderId of orderIds) {
      try {
        const response = await apiRequest(`/api/orders/${orderId}/status`, {
          method: 'PUT',
          body: JSON.stringify({ status: newStatus })
        });

        if (response.success) {
          successCount++;
        } else {
          failCount++;
        }
      } catch (error) {
        console.error(`更新訂單 ${orderId} 失敗:`, error);
        failCount++;
      }
    }

    // 顯示結果
    if (successCount > 0) {
      showMessage(`成功更新 ${successCount} 個訂單狀態`, "success");
    }
    if (failCount > 0) {
      showMessage(`${failCount} 個訂單更新失敗`, "error");
    }

    // 重新載入訂單列表
    loadOrders({});

  } catch (error) {
    console.error("批次更新訂單狀態錯誤:", error);
    showMessage("批次更新失敗: " + error.message, "error");
  } finally {
    hideLoading();
  }
}

// 強制快取更新 - 2025-08-11 16:27
console.log('deleteRole function loaded at:', new Date().toISOString());

// ================== 用戶審核管理功能 ====================

// 載入待審核用戶列表
async function loadPendingUsers() {
  console.log('載入待審核用戶列表');

  try {
    showLoading();

    // 暫時使用調試 API 來獲取用戶資料
    const response = await apiRequest('/api/debug/users');

    if (response.success) {
      // 管理員帳號列表（不應該出現在審核名單中）
      const adminUsernames = ['admin', 'super_admin', 'root', 'administrator', 'system'];

      // 只顯示真正的待審核用戶，並排除管理員帳號
      const pendingUsers = response.data.filter(user =>
        user.status === 'pending' &&
        !adminUsernames.includes(user.username.toLowerCase())
      );

      renderPendingUsers(pendingUsers);
      updateApprovalStats(pendingUsers);
      console.log(`成功載入 ${pendingUsers.length} 個待審核用戶`);

      if (pendingUsers.length > 0) {
        showMessage(`載入了 ${pendingUsers.length} 個待審核用戶`, 'success');
      } else {
        showMessage('目前沒有待審核的用戶', 'info');
      }
    } else {
      throw new Error(response.error || '載入用戶資料失敗');
    }
  } catch (error) {
    console.error('載入待審核用戶錯誤:', error);
    showMessage('載入待審核用戶失敗: ' + error.message, 'error');

    // 顯示空狀態
    const container = document.getElementById('pending-users-list');
    if (container) {
      container.innerHTML = `
        <div class="no-pending-users">
          <div class="no-data-icon">👥</div>
          <h3>載入失敗</h3>
          <p>無法載入待審核用戶列表</p>
          <button onclick="loadPendingUsers()" class="btn btn-primary">重試</button>
        </div>
      `;
    }
  } finally {
    hideLoading();
  }
}

// 渲染待審核用戶列表
function renderPendingUsers(users) {
  const container = document.getElementById('pending-users-list');
  if (!container) return;

  if (users.length === 0) {
    container.innerHTML = `
      <div class="no-pending-users">
        <div class="no-data-icon">✅</div>
        <h3>沒有待審核用戶</h3>
        <p>目前沒有需要審核的用戶註冊申請</p>
      </div>
    `;
    return;
  }

  container.innerHTML = users.map(user => `
    <div class="pending-user-card" data-user-id="${user.id}">
      <div class="user-card-header">
        <div class="user-selection">
          <input type="checkbox" class="user-checkbox" value="${user.id}">
        </div>
        <div class="user-basic-info">
          <h4 class="user-name">${user.username}</h4>
          <p class="user-email">${user.email}</p>
        </div>
        <div class="user-status">
          <span class="status-badge pending">待審核</span>
          <span class="submit-time">${formatDateTime(user.submitted_at)}</span>
        </div>
      </div>

      <div class="user-card-body">
        <div class="user-details">
          <div class="detail-item">
            <label>機構名稱：</label>
            <span>${user.pharmacy_name}</span>
          </div>
          ${user.contact_person ? `
            <div class="detail-item">
              <label>聯絡人：</label>
              <span>${user.contact_person}</span>
            </div>
          ` : ''}
          ${user.phone ? `
            <div class="detail-item">
              <label>聯絡電話：</label>
              <span>${user.phone}</span>
            </div>
          ` : ''}
          ${user.mobile ? `
            <div class="detail-item">
              <label>手機號碼：</label>
              <span>${user.mobile}</span>
            </div>
          ` : ''}
          ${user.institution_code ? `
            <div class="detail-item">
              <label>機構代號：</label>
              <span>${user.institution_code}</span>
            </div>
          ` : ''}
          ${user.address ? `
            <div class="detail-item">
              <label>地址：</label>
              <span>${user.address}</span>
            </div>
          ` : ''}
        </div>
      </div>

      <div class="user-card-actions">
        <button onclick="viewUserDetail(${user.id})" class="btn btn-outline">
          <span>👁️</span> 查看詳情
        </button>
        <button onclick="approveUser(${user.id})" class="btn btn-success">
          <span>✅</span> 通過
        </button>
        <button onclick="showRejectModal(${user.id})" class="btn btn-danger">
          <span>❌</span> 拒絕
        </button>
      </div>
    </div>
  `).join('');

  // 更新選擇計數
  updateSelectedCount();
}

// 更新審核統計
function updateApprovalStats(users) {
  const pendingCount = users.length;
  const pendingCountEl = document.getElementById('pending-count');
  if (pendingCountEl) {
    pendingCountEl.textContent = pendingCount;
  }

  // 今日已審核數量（這裡用模擬數據，實際應該從 API 獲取）
  const approvedTodayEl = document.getElementById('approved-today');
  if (approvedTodayEl) {
    approvedTodayEl.textContent = '0'; // 實際應該從 API 獲取
  }
}

// 審核用戶（通過）
async function approveUser(userId) {
  if (!confirm('確定要通過此用戶的註冊申請嗎？')) {
    return;
  }

  try {
    showLoading();

    const response = await apiRequest('/api/simple-approve', {
      method: 'POST',
      body: JSON.stringify({
        user_id: userId,
        action: 'approve'
      })
    });

    if (response.success) {
      showMessage(response.message, 'success');
    } else {
      throw new Error(response.error || '審核失敗');
    }

    // 從界面中移除已審核的用戶
    const userCard = document.querySelector(`[data-user-id="${userId}"]`);
    if (userCard) {
      userCard.style.opacity = '0.5';
      userCard.style.pointerEvents = 'none';
      setTimeout(() => {
        userCard.remove();
        updateSelectedCount();
        // 更新統計數據
        const pendingCountEl = document.getElementById('pending-count');
        if (pendingCountEl) {
          const currentCount = parseInt(pendingCountEl.textContent) || 0;
          pendingCountEl.textContent = Math.max(0, currentCount - 1);
        }
        const approvedTodayEl = document.getElementById('approved-today');
        if (approvedTodayEl) {
          const currentApproved = parseInt(approvedTodayEl.textContent) || 0;
          approvedTodayEl.textContent = currentApproved + 1;
        }
      }, 500);
    }

  } catch (error) {
    console.error('審核用戶錯誤:', error);
    showMessage('審核失敗: ' + error.message, 'error');
  } finally {
    hideLoading();
  }
}

// 顯示拒絕原因模態框
function showRejectModal(userId) {
  const modal = document.getElementById('reject-reason-modal');
  const userIdInput = document.getElementById('reject-user-id');
  const reasonTextarea = document.getElementById('reject-reason');

  if (modal && userIdInput && reasonTextarea) {
    userIdInput.value = userId;
    reasonTextarea.value = '';
    modal.style.display = 'block';
    reasonTextarea.focus();
  }
}

// 拒絕用戶
async function rejectUser(userId, reason) {
  try {
    showLoading();

    const response = await apiRequest('/api/simple-approve', {
      method: 'POST',
      body: JSON.stringify({
        user_id: userId,
        action: 'reject',
        reason: reason
      })
    });

    if (response.success) {
      showMessage(response.message, 'success');
    } else {
      throw new Error(response.error || '拒絕失敗');
    }

    // 從界面中移除已審核的用戶
    const userCard = document.querySelector(`[data-user-id="${userId}"]`);
    if (userCard) {
      userCard.style.opacity = '0.5';
      userCard.style.pointerEvents = 'none';
      setTimeout(() => {
        userCard.remove();
        updateSelectedCount();
        // 更新統計數據
        const pendingCountEl = document.getElementById('pending-count');
        if (pendingCountEl) {
          const currentCount = parseInt(pendingCountEl.textContent) || 0;
          pendingCountEl.textContent = Math.max(0, currentCount - 1);
        }
        const approvedTodayEl = document.getElementById('approved-today');
        if (approvedTodayEl) {
          const currentApproved = parseInt(approvedTodayEl.textContent) || 0;
          approvedTodayEl.textContent = currentApproved + 1;
        }
      }, 500);
    }

    // 關閉模態框
    const modal = document.getElementById('reject-reason-modal');
    if (modal) modal.style.display = 'none';

  } catch (error) {
    console.error('拒絕用戶錯誤:', error);
    showMessage('拒絕失敗: ' + error.message, 'error');
  } finally {
    hideLoading();
  }
}

// 查看用戶詳情
async function viewUserDetail(userId) {
  try {
    showLoading();

    // 這裡應該調用 API 獲取用戶詳細資料
    // 暫時使用模擬數據
    const modal = document.getElementById('user-detail-modal');
    const content = document.getElementById('user-detail-content');

    if (modal && content) {
      content.innerHTML = `
        <div class="user-detail-info">
          <h4>用戶詳細資料</h4>
          <p>用戶 ID: ${userId}</p>
          <p>詳細資料載入中...</p>
        </div>
      `;
      modal.style.display = 'block';
    }
  } catch (error) {
    console.error('載入用戶詳情錯誤:', error);
    showMessage('載入用戶詳情失敗: ' + error.message, 'error');
  } finally {
    hideLoading();
  }
}

// 批量操作相關函數
function updateSelectedCount() {
  const checkboxes = document.querySelectorAll('.user-checkbox:checked');
  const count = checkboxes.length;
  const countEl = document.getElementById('selected-users-count');
  const batchApproveBtn = document.getElementById('batch-approve');
  const batchRejectBtn = document.getElementById('batch-reject');

  if (countEl) {
    countEl.textContent = `已選擇 ${count} 個用戶`;
  }

  if (batchApproveBtn) {
    batchApproveBtn.disabled = count === 0;
  }

  if (batchRejectBtn) {
    batchRejectBtn.disabled = count === 0;
  }
}

// 全選/取消全選
function toggleSelectAllUsers(selectAll) {
  const checkboxes = document.querySelectorAll('.user-checkbox');
  checkboxes.forEach(checkbox => {
    checkbox.checked = selectAll;
  });
  updateSelectedCount();
}

// 批量審核
async function batchApproveUsers(action) {
  const checkboxes = document.querySelectorAll('.user-checkbox:checked');
  const userIds = Array.from(checkboxes).map(cb => parseInt(cb.value));

  if (userIds.length === 0) {
    showMessage('請先選擇要審核的用戶', 'warning');
    return;
  }

  const actionText = action === 'approve' ? '通過' : '拒絕';
  if (!confirm(`確定要${actionText} ${userIds.length} 個用戶嗎？`)) {
    return;
  }

  let reason = null;
  if (action === 'reject') {
    reason = prompt('請輸入拒絕原因：');
    if (!reason || reason.trim() === '') {
      showMessage('拒絕用戶時必須提供原因', 'error');
      return;
    }
  }

  try {
    showLoading();

    let processed = 0;
    let failed = [];

    // 逐個處理每個用戶
    for (const userId of userIds) {
      try {
        const response = await apiRequest('/api/simple-approve', {
          method: 'POST',
          body: JSON.stringify({
            user_id: userId,
            action: action,
            reason: reason
          })
        });

        if (response.success) {
          processed++;
        } else {
          failed.push(userId);
        }
      } catch (error) {
        console.error(`批量審核用戶 ${userId} 失敗:`, error);
        failed.push(userId);
      }
    }

    const message = failed.length === 0
      ? `成功${actionText}了 ${processed} 個用戶`
      : `成功${actionText}了 ${processed} 個用戶，${failed.length} 個失敗`;

    showMessage(message, failed.length === 0 ? 'success' : 'warning');

    // 從界面中移除已審核的用戶
    userIds.forEach(userId => {
      const userCard = document.querySelector(`[data-user-id="${userId}"]`);
      if (userCard) {
        userCard.style.opacity = '0.5';
        userCard.style.pointerEvents = 'none';
        setTimeout(() => {
          userCard.remove();
        }, 500);
      }
    });

    // 更新統計數據
    setTimeout(() => {
      const pendingCountEl = document.getElementById('pending-count');
      if (pendingCountEl) {
        const currentCount = parseInt(pendingCountEl.textContent) || 0;
        pendingCountEl.textContent = Math.max(0, currentCount - userIds.length);
      }
      const approvedTodayEl = document.getElementById('approved-today');
      if (approvedTodayEl) {
        const currentApproved = parseInt(approvedTodayEl.textContent) || 0;
        approvedTodayEl.textContent = currentApproved + userIds.length;
      }
      updateSelectedCount();
    }, 600);

  } catch (error) {
    console.error('批量審核錯誤:', error);
    showMessage('批量審核失敗: ' + error.message, 'error');
  } finally {
    hideLoading();
  }
}

// 設置審核相關事件監聽器
function setupApprovalEventListeners() {
  // 拒絕原因表單提交
  document.addEventListener('submit', function (e) {
    if (e.target && e.target.id === 'reject-reason-form') {
      e.preventDefault();
      const userId = document.getElementById('reject-user-id').value;
      const reason = document.getElementById('reject-reason').value.trim();

      if (userId && reason) {
        rejectUser(parseInt(userId), reason);
      }
    }
  });

  // 模態框關閉按鈕
  document.addEventListener('click', function (e) {
    if (e.target && e.target.id === 'close-user-detail-modal') {
      const modal = document.getElementById('user-detail-modal');
      if (modal) modal.style.display = 'none';
    }

    if (e.target && e.target.id === 'close-reject-reason-modal') {
      const modal = document.getElementById('reject-reason-modal');
      if (modal) modal.style.display = 'none';
    }

    if (e.target && e.target.id === 'cancel-reject') {
      const modal = document.getElementById('reject-reason-modal');
      if (modal) modal.style.display = 'none';
    }

    // 全選/取消全選
    if (e.target && e.target.id === 'select-all-users') {
      toggleSelectAllUsers(true);
    }

    if (e.target && e.target.id === 'deselect-all-users') {
      toggleSelectAllUsers(false);
    }

    // 批量操作
    if (e.target && e.target.id === 'batch-approve') {
      batchApproveUsers('approve');
    }

    if (e.target && e.target.id === 'batch-reject') {
      batchApproveUsers('reject');
    }

    // 重新整理
    if (e.target && e.target.id === 'refresh-pending') {
      loadPendingUsers();
    }
  });

  // 用戶選擇框變化
  document.addEventListener('change', function (e) {
    if (e.target && e.target.classList.contains('user-checkbox')) {
      updateSelectedCount();
    }
  });

  // 搜尋功能
  document.addEventListener('input', function (e) {
    if (e.target && e.target.id === 'user-search') {
      filterPendingUsers(e.target.value);
    }
  });
}

// 過濾待審核用戶
function filterPendingUsers(searchTerm) {
  const userCards = document.querySelectorAll('.pending-user-card');
  const term = searchTerm.toLowerCase();

  userCards.forEach(card => {
    const userName = card.querySelector('.user-name').textContent.toLowerCase();
    const userEmail = card.querySelector('.user-email').textContent.toLowerCase();
    const pharmacyName = card.querySelector('.detail-item span').textContent.toLowerCase();

    const matches = userName.includes(term) ||
      userEmail.includes(term) ||
      pharmacyName.includes(term);

    card.style.display = matches ? 'block' : 'none';
  });
}

// 格式化日期時間
function formatDateTime(dateString) {
  const date = new Date(dateString);
  return date.toLocaleString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// 移除重複的 DOMContentLoaded 監聽器，這些函數將在主初始化函數中調用

// ==================== 產品管理功能 ====================

// 設置產品管理事件監聽器
function setupProductManagementEventListeners() {
  // 管理員標籤切換
  document.addEventListener('click', function (e) {
    if (e.target && e.target.classList.contains('admin-nav-tab')) {
      switchAdminTab(e.target.dataset.adminTab);
    }

    // 產品編輯模態框相關
    if (e.target && e.target.id === 'close-product-edit-modal') {
      closeProductEditModal();
    }

    if (e.target && e.target.id === 'cancel-product-edit') {
      closeProductEditModal();
    }

    // 管理員產品搜尋
    if (e.target && e.target.id === 'admin-search-btn') {
      loadAdminProducts();
    }

    if (e.target && e.target.id === 'admin-clear-search-btn') {
      document.getElementById('admin-product-search').value = '';
      loadAdminProducts();
    }

    // 新增產品按鈕
    if (e.target && e.target.id === 'add-product-btn') {
      openAddProductModal();
    }
  });

  // 產品編輯表單提交
  document.addEventListener('submit', function (e) {
    if (e.target && e.target.id === 'product-edit-form') {
      e.preventDefault();
      handleProductEdit();
    }
  });

  // 篩選器變化
  document.addEventListener('change', function (e) {
    if (e.target && (e.target.id === 'category-filter' || e.target.id === 'stock-filter')) {
      loadAdminProducts();
    }
  });

  // 搜尋輸入
  document.addEventListener('keypress', function (e) {
    if (e.target && e.target.id === 'admin-product-search' && e.key === 'Enter') {
      loadAdminProducts();
    }
  });
}

// 切換管理員子標籤
function switchAdminTab(tabName) {
  // 隱藏所有子標籤內容
  document.querySelectorAll('.admin-tab-content').forEach(content => {
    content.classList.remove('active');
  });

  // 移除所有標籤的 active 狀態
  document.querySelectorAll('.admin-nav-tab').forEach(tab => {
    tab.classList.remove('active');
  });

  // 顯示選中的標籤內容
  const targetContent = document.getElementById(`${tabName}-admin`);
  if (targetContent) {
    targetContent.classList.add('active');
  }

  // 設置選中標籤為 active
  const targetTab = document.querySelector(`[data-admin-tab="${tabName}"]`);
  if (targetTab) {
    targetTab.classList.add('active');
  }

  // 如果切換到產品管理標籤，載入產品資料
  if (tabName === 'product-management') {
    loadAdminProducts();
  }
}

// 載入管理員產品列表
async function loadAdminProducts(page = 1) {
  try {
    showLoading();
    
    const searchTerm = document.getElementById('admin-product-search')?.value || '';
    const categoryFilter = document.getElementById('category-filter')?.value || '';
    const stockFilter = document.getElementById('stock-filter')?.value || '';
    
    let url = `/api/products?page=${page}&limit=20`;
    if (searchTerm) url += `&search=${encodeURIComponent(searchTerm)}`;
    if (categoryFilter) url += `&category=${categoryFilter}`;
    if (stockFilter) url += `&stock_status=${stockFilter}`;

    const response = await apiRequest(url);
    
    if (response.success) {
      renderAdminProductsTable(response.data.products || []);
      updateAdminPagination(response.data.pagination || {});
    } else {
      showMessage('載入產品失敗: ' + response.error, 'error');
    }
  } catch (error) {
    console.error('載入管理員產品錯誤:', error);
    showMessage('載入產品失敗', 'error');
  } finally {
    hideLoading();
  }
}

// 渲染管理員產品表格
function renderAdminProductsTable(products) {
  const tbody = document.getElementById('admin-products-table-body');
  if (!tbody) return;

  if (products.length === 0) {
    tbody.innerHTML = '<tr><td colspan="9" style="text-align: center; padding: 40px; color: #999;">沒有找到產品</td></tr>';
    return;
  }

  tbody.innerHTML = products.map(product => `
    <tr>
      <td>${product.nhi_code || 'N/A'}</td>
      <td>
        <div class="product-name-cell">
          <div class="product-title">${product.name || 'N/A'}</div>
          <div class="product-ingredients">${getIngredients(product)}</div>
        </div>
      </td>
      <td>${product.dosage_form || 'N/A'}</td>
      <td>${product.manufacturer || 'N/A'}</td>
      <td>$${product.nhi_price || '0'}</td>
      <td>$${product.selling_price || product.nhi_price || '0'}</td>
      <td>
        <input type="number" 
               class="stock-input"
               value="${product.stock_quantity || 0}" 
               min="0"
               onchange="updateProductStock(${product.id}, this.value)">
      </td>
      <td>
        <span class="status-badge ${getProductStatusClass(product)}">${getProductStatusText(product)}</span>
      </td>
      <td>
        <div class="admin-actions">
          <button class="btn btn-sm btn-primary" onclick="editProduct(${product.id})" title="編輯">
            ✏️
          </button>
          <button class="btn btn-sm btn-danger" onclick="deleteProduct(${product.id})" title="刪除">
            🗑️
          </button>
        </div>
      </td>
    </tr>
  `).join('');
}

// 更新管理員分頁
function updateAdminPagination(pagination) {
  const info = document.getElementById('admin-pagination-info');
  const pageInput = document.getElementById('admin-page-input');
  const prevBtn = document.getElementById('admin-prev-page');
  const nextBtn = document.getElementById('admin-next-page');

  if (info) {
    const start = ((pagination.current_page || 1) - 1) * (pagination.per_page || 20) + 1;
    const end = Math.min(start + (pagination.per_page || 20) - 1, pagination.total || 0);
    info.textContent = `顯示第 ${start}-${end} 筆，共 ${pagination.total || 0} 筆資料`;
  }

  if (pageInput) {
    pageInput.value = pagination.current_page || 1;
  }

  if (prevBtn) {
    prevBtn.disabled = (pagination.current_page || 1) <= 1;
    prevBtn.onclick = () => loadAdminProducts((pagination.current_page || 1) - 1);
  }

  if (nextBtn) {
    nextBtn.disabled = (pagination.current_page || 1) >= (pagination.total_pages || 1);
    nextBtn.onclick = () => loadAdminProducts((pagination.current_page || 1) + 1);
  }
}

// 編輯產品
async function editProduct(productId) {
  try {
    showLoading();
    
    // 獲取產品詳細資料
    const response = await apiRequest(`/api/products/${productId}`);
    
    if (response.success) {
      openProductEditModal(response.data);
    } else {
      showMessage('獲取產品資料失敗: ' + response.error, 'error');
    }
  } catch (error) {
    console.error('獲取產品資料錯誤:', error);
    showMessage('獲取產品資料失敗', 'error');
  } finally {
    hideLoading();
  }
}

// 打開產品編輯模態框
function openProductEditModal(product = null) {
  const modal = document.getElementById('product-edit-modal');
  const title = document.getElementById('product-edit-title');
  
  if (!modal) return;

  if (product) {
    // 編輯模式
    title.textContent = '編輯產品';
    fillProductEditForm(product);
  } else {
    // 新增模式
    title.textContent = '新增產品';
    clearProductEditForm();
  }

  modal.style.display = 'block';
}

// 填入產品編輯表單
function fillProductEditForm(product) {
  document.getElementById('edit-product-id').value = product.id || '';
  document.getElementById('edit-product-name').value = product.name || '';
  document.getElementById('edit-nhi-code').value = product.nhi_code || '';
  document.getElementById('edit-dosage-form').value = product.dosage_form || '';
  document.getElementById('edit-manufacturer').value = product.manufacturer || '';
  document.getElementById('edit-nhi-price').value = product.nhi_price || '';
  document.getElementById('edit-unit-price').value = product.selling_price || '';
  document.getElementById('edit-stock-quantity').value = product.stock_quantity !== undefined && product.stock_quantity !== null ? product.stock_quantity : '';
  document.getElementById('edit-unit').value = product.unit || '';
  document.getElementById('edit-description').value = product.description || '';
  document.getElementById('edit-ingredients').value = product.ingredients || '';
}

// 清空產品編輯表單
function clearProductEditForm() {
  document.getElementById('product-edit-form').reset();
  document.getElementById('edit-product-id').value = '';
}

// 關閉產品編輯模態框
function closeProductEditModal() {
  const modal = document.getElementById('product-edit-modal');
  if (modal) modal.style.display = 'none';
}

// 處理產品編輯提交
async function handleProductEdit() {
  try {
    showLoading();
    
    const formData = new FormData(document.getElementById('product-edit-form'));
    const productId = document.getElementById('edit-product-id').value;
    
    const productData = {
      name: formData.get('name'),
      nhi_code: formData.get('nhi_code'),
      dosage_form: formData.get('dosage_form'),
      manufacturer: formData.get('manufacturer'),
      nhi_price: parseFloat(formData.get('nhi_price')) || null,
      unit_price: parseFloat(formData.get('unit_price')) || null,
      stock_quantity: parseInt(formData.get('stock_quantity')) || 0,
      unit: formData.get('unit'),
      description: formData.get('description'),
      ingredients: formData.get('ingredients')
    };

    const url = productId ? `/api/products-admin/${productId}` : '/api/products-admin';
    const method = productId ? 'PUT' : 'POST';
    
    const response = await apiRequest(url, {
      method: method,
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(productData),
    });

    if (response.success) {
      showMessage(productId ? '產品更新成功！' : '產品新增成功！', 'success');
      closeProductEditModal();
      loadAdminProducts(); // 重新載入產品列表
      loadProducts(); // 重新載入一般產品列表
    } else {
      showMessage((productId ? '產品更新失敗: ' : '產品新增失敗: ') + response.error, 'error');
    }
  } catch (error) {
    console.error('產品編輯錯誤:', error);
    showMessage('操作失敗', 'error');
  } finally {
    hideLoading();
  }
}

// 更新產品庫存
async function updateProductStock(productId, newStock) {
  try {
    const response = await apiRequest(`/api/products-admin/${productId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        stock_quantity: parseInt(newStock)
      }),
    });

    if (response.success) {
      showMessage('庫存更新成功', 'success');
    } else {
      showMessage('庫存更新失敗: ' + response.error, 'error');
      // 恢復原值
      loadAdminProducts();
    }
  } catch (error) {
    console.error('更新庫存錯誤:', error);
    showMessage('庫存更新失敗', 'error');
    loadAdminProducts();
  }
}

// 刪除產品
async function deleteProduct(productId) {
  if (!confirm('確定要刪除這個產品嗎？此操作無法復原。')) {
    return;
  }

  try {
    showLoading();
    
    const response = await apiRequest(`/api/products-admin/${productId}`, {
      method: 'DELETE'
    });

    if (response.success) {
      showMessage('產品刪除成功', 'success');
      loadAdminProducts(); // 重新載入產品列表
      loadProducts(); // 重新載入一般產品列表
    } else {
      showMessage('產品刪除失敗: ' + response.error, 'error');
    }
  } catch (error) {
    console.error('刪除產品錯誤:', error);
    showMessage('產品刪除失敗', 'error');
  } finally {
    hideLoading();
  }
}

// 打開新增產品模態框
function openAddProductModal() {
  openProductEditModal(null);
}

// 獲取產品狀態樣式類
function getProductStatusClass(product) {
  const stock = product.stock_quantity || 0;
  if (stock > 10) return 'in-stock';
  if (stock > 0) return 'low-stock';
  return 'out-of-stock';
}

// 獲取產品狀態文字
function getProductStatusText(product) {
  const stock = product.stock_quantity || 0;
  if (stock > 10) return '供貨中';
  if (stock > 0) return '庫存不足';
  return '缺貨';
}

// 列印訂單功能（僅管理者可用）
async function printOrder(orderId) {
  // 檢查權限
  if (!currentUser || !currentUser.permissions || currentUser.permissions.role_name !== 'admin') {
    showMessage('只有管理者可以列印訂單', 'error');
    return;
  }

  try {
    showLoading();
    
    // 調用後端列印API
    const response = await fetch(`${API_BASE}/api/print/orders/${orderId}?include_prices=true`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      // 獲取HTML內容
      const htmlContent = await response.text();
      
      // 在新窗口中打開列印內容
      const printWindow = window.open('', '_blank');
      printWindow.document.write(htmlContent);
      printWindow.document.close();
      
      // 等待內容載入完成後觸發列印
      printWindow.onload = function() {
        printWindow.print();
      };
      
      showMessage('列印頁面已準備完成', 'success');
    } else {
      // 嘗試獲取錯誤訊息
      let errorMessage = '列印請求失敗';
      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorMessage;
      } catch {
        // 如果不是JSON格式，直接使用狀態文字
        errorMessage = `列印請求失敗 (${response.status})`;
      }
      throw new Error(errorMessage);
    }
  } catch (error) {
    console.error('列印訂單錯誤:', error);
    showMessage('列印訂單失敗: ' + error.message, 'error');
  } finally {
    hideLoading();
  }
}