// 強制修復產品顯示問題
console.log('🚀 開始強制修復產品顯示...');

(function() {
    try {
        const gridBody = document.getElementById('products-grid-body');
        if (!gridBody) {
            console.error('❌ 找不到products-grid-body元素');
            return;
        }
        
        console.log('✅ 找到products-grid-body元素');
        
        // 檢查產品資料
        if (typeof currentProducts === 'undefined' || !currentProducts || currentProducts.length === 0) {
            console.error('❌ 沒有產品資料');
            return;
        }
        
        console.log(`📦 發現 ${currentProducts.length} 筆產品資料`);
        
        // 強制清除並重新渲染
        gridBody.innerHTML = '';
        
        // 使用簡化的佈局，不使用CSS Grid
        const html = currentProducts.map(product => `
            <div class="product-card-simple" style="
                display: flex;
                align-items: center;
                padding: 15px;
                border-bottom: 1px solid #eee;
                background: white;
                min-height: 60px;
            ">
                <div style="flex: 1; padding: 10px; border-right: 1px solid #eee;">
                    <div style="font-weight: bold; color: #333;">${product.nhi_code || 'N/A'}</div>
                    <div style="font-size: 12px; color: #666;">健保價: ${product.nhi_price || 'N/A'}</div>
                </div>
                
                <div style="flex: 2; padding: 10px; border-right: 1px solid #eee;">
                    <div style="font-weight: bold; color: #333;">${product.name || 'N/A'}</div>
                    <div style="font-size: 12px; color: #666;">主要成分</div>
                </div>
                
                <div style="flex: 1; padding: 10px; border-right: 1px solid #eee; text-align: center;">
                    ${product.dosage_form || 'N/A'}
                </div>
                
                <div style="flex: 1; padding: 10px; border-right: 1px solid #eee; text-align: center;">
                    NT$ ${product.unit_price || '0'}
                </div>
                
                <div style="flex: 1; padding: 10px; border-right: 1px solid #eee; text-align: center;">
                    <input type="number" min="1" value="1" style="width: 60px; padding: 5px;">
                </div>
                
                <div style="flex: 1; padding: 10px; border-right: 1px solid #eee; text-align: center;">
                    <button style="padding: 5px 10px; margin: 2px;">🛒</button>
                    <button style="padding: 5px 10px; margin: 2px;">❤️</button>
                </div>
                
                <div style="flex: 1; padding: 10px; text-align: center;">
                    <span style="background: #28a745; color: white; padding: 3px 8px; border-radius: 12px; font-size: 12px;">供貨中</span>
                </div>
            </div>
        `).join('');
        
        gridBody.innerHTML = html;
        
        // 強制設定容器樣式
        gridBody.style.display = 'block';
        gridBody.style.visibility = 'visible';
        gridBody.style.height = 'auto';
        gridBody.style.width = '100%';
        gridBody.style.overflow = 'visible';
        
        // 檢查父容器
        const container = gridBody.closest('.products-grid-container');
        if (container) {
            container.style.maxHeight = 'none';
            container.style.overflow = 'visible';
        }
        
        console.log('✅ 強制修復完成！產品應該現在可見了');
        
        // 更新分頁資訊
        const paginationInfo = document.getElementById('pagination-info');
        if (paginationInfo) {
            paginationInfo.textContent = `顯示第 1-${currentProducts.length} 筆，共 ${currentProducts.length} 筆資料`;
        }
        
    } catch (error) {
        console.error('❌ 強制修復失敗:', error);
    }
})();
