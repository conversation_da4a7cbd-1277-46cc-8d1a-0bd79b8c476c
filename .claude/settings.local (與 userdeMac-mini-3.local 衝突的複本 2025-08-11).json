{"permissions": {"allow": ["Bash(cargo check:*)", "Bash(cargo fix:*)", "Bash(cargo test:*)", "Bash(ls:*)", "Bash(cargo run:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(chmod:*)", "Bash(sqlite3:*)", "Bash(cargo build:*)", "<PERSON><PERSON>(pkill:*)", "Bash(pgrep:*)", "Bash(rustc:*)", "Bash(./test_rounding)", "Bash(rm:*)", "Bash(rg:*)", "Bash(find:*)", "Bash(node:*)", "Bash(./test_backend_rounding.sh:*)", "Bash(./test_order_notes.sh:*)", "Bash(grep:*)", "<PERSON><PERSON>(python3:*)", "Bash(pip3 install:*)", "Bash(cp:*)", "<PERSON><PERSON>(claude doctor)", "Bash(claude --version)", "Bash(./test_compile.sh)", "Bash(RUST_LOG=debug cargo run --bin pharmacy-system)", "Bash(timeout 10 cargo run:*)", "Bash(PORT=8081 cargo run --bin pharmacy-system)", "Bash(RUST_LOG=debug PORT=8081 cargo run --bin pharmacy-system)", "<PERSON><PERSON>(touch:*)", "Bash(./start.sh:*)", "Bash(PORT=8080 cargo run --bin pharmacy-system)", "Bash(RUST_LOG=debug PORT=8080 cargo run --bin pharmacy-system)", "Bash(RUST_LOG=trace,sqlx=debug PORT=8080 cargo run --bin pharmacy-system)", "<PERSON><PERSON>(sed:*)", "Bash(RUST_LOG=info PORT=8080 cargo run --bin pharmacy-system)", "<PERSON><PERSON>(timeout:*)", "Bash(RUST_LOG=trace PORT=8080 cargo run --bin pharmacy-system)", "Bash(RUST_LOG=info PORT=8080 ./target/debug/pharmacy-system)", "Bash(psql:*)", "<PERSON>sh(--max-time 2)", "Bash(cargo clean:*)", "Bash(RUST_LOG=debug timeout 10 cargo run --bin pharmacy-system)", "<PERSON><PERSON>(cat:*)", "Bash(RUST_LOG=info PORT=8080 timeout 10 cargo run --bin pharmacy-system)", "Bash(PORT=8080 timeout 10 cargo run --bin pharmacy-system)", "Bash(PORT=8080 ./target/debug/pharmacy-system)", "Bash(kill:*)", "Bash(PORT=8080 timeout 5 cargo run --bin pharmacy-system)", "Bash(PGPASSWORD=$DATABASE_PASSWORD:-password psql -h $DATABASE_HOST:-localhost -p $DATABASE_PORT:-5432 -U $DATABASE_USER:-happyorder -d $DATABASE_NAME:-happyorder -c \"\\d nhi_prices\")", "Bash(RUST_LOG=info PORT=8080 timeout 10 ./target/debug/pharmacy-system)", "Bash(claude migrate-installer:*)", "Bash(./test_price_queries)", "Bash(brew services start:*)", "<PERSON><PERSON>(source .env)"], "deny": []}}