mod models;
mod services;
mod repositories;
mod api;
mod config;
mod error;
mod database;
mod logging;

use std::net::SocketAddr;
use crate::logging::{LoggingConfig, init_logging};

use crate::{
    config::Config,
    database::Database,
    api::{create_api_router, auth::AppState},
    repositories::{
        order::{OrderRepository, PostgresOrderRepository},
        cart::{CartRepository, PostgresCartRepository},
        product::{ProductRepository, PostgresProductRepository},
        user::{UserRepository, PostgresUserRepository},
    },
    services::{
        order::{OrderService, OrderServiceImpl},
        product::{ProductService, ProductServiceImpl},
        file_processing::{FileProcessingService, FileProcessingServiceImpl},
        auth::{AuthService, AuthServiceImpl},
        cart::{CartService, CartServiceImpl},
        backup::{BackupService, BackupServiceImpl},
        notification::{NotificationService, NotificationServiceImpl},
        // admin::initialize_system,
    },
};
use std::sync::Arc;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 載入環境變數
    dotenvy::dotenv().ok();

    // 初始化日誌系統
    let logging_config = LoggingConfig::default();
    init_logging(logging_config)
        .map_err(|e| format!("Failed to initialize logging: {}", e))?;

    // 載入配置
    let config = Config::from_env()
        .map_err(|e| format!("Failed to load config: {}", e))?;

    // 建立資料庫連線
    let database = Database::new(&config.database_url).await
        .map_err(|e| format!("Failed to connect to database: {}", e))?;

    let db_pool = database.pool().clone();

    // 建立 repositories
    let _user_repository: Arc<dyn UserRepository> = Arc::new(PostgresUserRepository::new(db_pool.clone()));
    let order_repository: Arc<dyn OrderRepository> = Arc::new(PostgresOrderRepository::new(db_pool.clone()));
    let cart_repository: Arc<dyn CartRepository> = Arc::new(PostgresCartRepository::new(db_pool.clone()));
    let product_repository: Arc<dyn ProductRepository> = Arc::new(PostgresProductRepository::new(db_pool.clone()));

    // 建立 services
    let file_processing_service: Arc<dyn FileProcessingService> = Arc::new(FileProcessingServiceImpl::new());
    let auth_service: Arc<dyn AuthService> = Arc::new(AuthServiceImpl::new(
        db_pool.clone(),
        config.clone(),
    ));
    let product_service: Arc<dyn ProductService> = Arc::new(ProductServiceImpl::new(
        product_repository.clone(),
        file_processing_service,
    ));
    let cart_service: Arc<dyn CartService> = Arc::new(CartServiceImpl::new(
        cart_repository.clone(),
        product_service.clone(),
    ));
    let order_service: Arc<dyn OrderService> = Arc::new(OrderServiceImpl::new(
        order_repository.clone(),
        cart_repository.clone(),
        product_service.clone(),
    ));
    tracing::info!("Creating backup service...");
    let backup_service: Arc<dyn BackupService> = Arc::new(BackupServiceImpl::new(
        db_pool.clone(),
        config.gcp.storage_bucket.clone(),
        config.gcp.credentials_path.clone(),
    ).await.map_err(|e| format!("Failed to create backup service: {}", e))?);
    tracing::info!("Backup service created successfully");
    
    tracing::info!("Creating notification service...");
    let notification_service: Arc<dyn NotificationService> = Arc::new(NotificationServiceImpl::new(
        config.clone(),
    ));
    tracing::info!("Notification service created successfully");

    // 初始化系統（創建預設角色和超級管理員）
    // TODO: 暫時註解掉，因為 CockroachDB 不支援 sqlx 遷移的 pg_advisory_lock
    // tracing::info!("正在初始化系統...");
    // if let Err(e) = initialize_system(db_pool.clone(), config.clone()).await {
    //     tracing::error!("系統初始化失敗: {}", e);
    //     return Err(format!("Failed to initialize system: {}", e).into());
    // }
    // tracing::info!("系統初始化完成");

    // 建立應用狀態
    let app_state = AppState {
        db: db_pool,
        config: config.clone(),
        auth_service,
        product_service,
        cart_service,
        order_service,
        backup_service,
        notification_service,
    };

    // 建立完整的 API 路由器
    let app = create_api_router(app_state);

    // 添加調試訊息
    tracing::info!("API routes configured:");
    tracing::info!("  - / (API info)");
    tracing::info!("  - /health (Health check)");
    tracing::info!("  - /api/auth/* (Authentication)");
    tracing::info!("  - /api/products/* (Product management)");
    tracing::info!("  - /api/orders/* (Order management)");
    tracing::info!("  - /api/cart/* (Cart management)");
    tracing::info!("  - /api/backup/* (Backup operations)");
    tracing::info!("  - /api/notifications/* (Notification services)");

    // 啟動伺服器
    let addr = SocketAddr::from(([0, 0, 0, 0], config.server_port));
    tracing::info!("Server starting on {}", addr);
    
    let listener = tokio::net::TcpListener::bind(addr).await?;
    tracing::info!("Server bound to address successfully, now serving requests...");
    tracing::info!("🚀 Pharmacy System is now running on http://{}", addr);
    axum::serve(listener, app).await?;

    Ok(())
}

