use axum::{
    extract::{<PERSON>, Query, State, <PERSON><PERSON> as J<PERSON>Extractor, Extension},
    response::J<PERSON>,
    routing::{get, post, put},
    Router,
};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use crate::models::{OrderF<PERSON>er, CreateOrderRequest, OrderStatus};
use crate::error::AppResult;
use crate::api::auth::AppState;

pub fn order_routes() -> Router<AppState> {
    Router::new()
        .route("/", post(create_order_from_request))
        .route("/cart", post(create_order_from_cart))
        .route("/", get(list_orders))
        .route("/all", get(list_all_orders).layer(axum::middleware::from_fn(crate::api::middleware::require_view_all_orders_middleware)))
        .route("/count", get(get_order_count))
        .route("/:id", get(get_order_details))
        .route("/number/:order_number", get(get_order_by_number))
        .route("/:id/status", put(update_order_status).layer(axum::middleware::from_fn(crate::api::middleware::require_admin_middleware)))
        .route("/:id/cancel", put(cancel_order))
}

// 訂單項目相關路由
pub fn order_item_routes() -> Router<AppState> {
    Router::new()
        .route("/:item_id/stock-status", put(update_order_item_stock_status).layer(axum::middleware::from_fn(crate::api::middleware::require_admin_middleware)))
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OrderQueryParams {
    pub status: Option<String>,
    pub start_date: Option<String>,
    pub end_date: Option<String>,
    pub page: Option<u32>,
    pub limit: Option<u32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateOrderStatusRequest {
    pub status: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateOrderItemStockStatusRequest {
    pub is_stock_out: bool,
}

/// 從購物車創建訂單
async fn create_order_from_cart(
    State(state): State<AppState>,
    Extension(claims): Extension<crate::api::middleware::Claims>,
    JsonExtractor(request): JsonExtractor<Option<serde_json::Value>>,
) -> AppResult<Json<Value>> {
    let user_id = claims.user_id;
    
    // 提取備註
    let notes = request
        .as_ref()
        .and_then(|v| v.get("notes"))
        .and_then(|n| n.as_str())
        .map(|s| s.to_string());
    
    let order_details = state.order_service.create_order_from_cart_with_notes(user_id, notes).await?;
    
    Ok(Json(json!({
        "success": true,
        "message": "Order created successfully from cart",
        "data": {
            "order": order_details
        }
    })))
}

/// 從請求創建訂單
async fn create_order_from_request(
    State(state): State<AppState>,
    Extension(claims): Extension<crate::api::middleware::Claims>,
    JsonExtractor(request): JsonExtractor<CreateOrderRequest>,
) -> AppResult<Json<Value>> {
    let user_id = claims.user_id;
    
    let order_details = state.order_service.create_order_from_request(user_id, request).await?;
    
    Ok(Json(json!({
        "success": true,
        "message": "Order created successfully",
        "data": {
            "order": order_details
        }
    })))
}

/// 查詢訂單列表（支援篩選和分頁）
async fn list_orders(
    State(state): State<AppState>,
    Extension(claims): Extension<crate::api::middleware::Claims>,
    Query(params): Query<OrderQueryParams>,
) -> AppResult<Json<Value>> {
    let user_id = claims.user_id;
    
    // 解析篩選條件
    let filter = if params.status.is_some() || params.start_date.is_some() || params.end_date.is_some() {
        let status = params.status.as_ref().and_then(|s| match s.as_str() {
            "pending" => Some(OrderStatus::待處理),
            
            "processing" => Some(OrderStatus::揀貨中),
            "shipped" => Some(OrderStatus::已出貨),
            
            
            _ => None,
        });
        
        let start_date = params.start_date.as_ref()
            .and_then(|s| chrono::DateTime::parse_from_rfc3339(s).ok())
            .map(|dt| dt.with_timezone(&chrono::Utc));
            
        let end_date = params.end_date.as_ref()
            .and_then(|s| chrono::DateTime::parse_from_rfc3339(s).ok())
            .map(|dt| dt.with_timezone(&chrono::Utc));
        
        Some(OrderFilter {
            status,
            start_date,
            end_date,
            page: params.page,
            limit: params.limit,
        })
    } else {
        None
    };
    
    let orders = state.order_service.list_user_orders(
        user_id, 
        filter, 
        params.page, 
        params.limit
    ).await?;
    
    let total_count = state.order_service.get_order_count(user_id, None).await?;
    
    Ok(Json(json!({
        "success": true,
        "data": {
            "orders": orders,
            "pagination": {
                "page": params.page.unwrap_or(1),
                "limit": params.limit.unwrap_or(50),
                "total": total_count
            }
        }
    })))
}

/// 獲取訂單詳情
async fn get_order_details(
    State(state): State<AppState>,
    Extension(claims): Extension<crate::api::middleware::Claims>,
    Extension(auth_context): Extension<crate::api::middleware::AuthContext>,
    Path(order_id): Path<i64>,
) -> AppResult<Json<Value>> {
    let user_id = claims.user_id;
    
    // 使用權限系統判斷：管理員可以查看任何訂單，一般用戶只能查看自己的訂單
    let order_details = if auth_context.can_view_all_orders() {
        // 管理員：直接通過repository獲取訂單詳情，不檢查用戶ID
        state.order_service.get_order_details_by_id(order_id).await?
    } else {
        // 一般用戶：檢查用戶ID
        state.order_service.get_order(order_id, user_id).await?
    };
    
    match order_details {
        Some(details) => Ok(Json(json!({
            "success": true,
            "data": {
                "order": details
            }
        }))),
        None => Ok(Json(json!({
            "success": false,
            "message": "Order not found"
        })))
    }
}

/// 根據訂單編號獲取訂單詳情
async fn get_order_by_number(
    State(state): State<AppState>,
    Extension(claims): Extension<crate::api::middleware::Claims>,
    Path(order_number): Path<String>,
) -> AppResult<Json<Value>> {
    let user_id = claims.user_id;
    
    let order_details = state.order_service.get_order_by_number(&order_number, user_id).await?;
    
    match order_details {
        Some(details) => Ok(Json(json!({
            "success": true,
            "data": {
                "order": details
            }
        }))),
        None => Ok(Json(json!({
            "success": false,
            "message": "Order not found"
        })))
    }
}

/// 獲取訂單數量統計
async fn get_order_count(
    State(state): State<AppState>,
    Extension(claims): Extension<crate::api::middleware::Claims>,
    Query(params): Query<OrderQueryParams>,
) -> AppResult<Json<Value>> {
    let user_id = claims.user_id;
    
    // 解析篩選條件
    let filter = if params.status.is_some() || params.start_date.is_some() || params.end_date.is_some() {
        let status = params.status.as_ref().and_then(|s| match s.as_str() {
            "pending" => Some(OrderStatus::待處理),
            
            "processing" => Some(OrderStatus::揀貨中),
            "shipped" => Some(OrderStatus::已出貨),
            
            
            _ => None,
        });
        
        let start_date = params.start_date.as_ref()
            .and_then(|s| chrono::DateTime::parse_from_rfc3339(s).ok())
            .map(|dt| dt.with_timezone(&chrono::Utc));
            
        let end_date = params.end_date.as_ref()
            .and_then(|s| chrono::DateTime::parse_from_rfc3339(s).ok())
            .map(|dt| dt.with_timezone(&chrono::Utc));
        
        Some(OrderFilter {
            status,
            start_date,
            end_date,
            page: params.page,
            limit: params.limit,
        })
    } else {
        None
    };
    
    let count = state.order_service.get_order_count(user_id, filter).await?;
    
    Ok(Json(json!({
        "success": true,
        "data": {
            "count": count
        }
    })))
}

/// 更新訂單狀態（管理員專用）
async fn update_order_status(
    State(state): State<AppState>,
    Extension(_claims): Extension<crate::api::middleware::Claims>,
    Path(order_id): Path<i64>,
    JsonExtractor(request): JsonExtractor<UpdateOrderStatusRequest>,
) -> AppResult<Json<Value>> {
    let status = match request.status.as_str() {
        "待處理" | "pending" => OrderStatus::待處理,
        "揀貨中" | "processing" => OrderStatus::揀貨中,
        "已出貨" | "shipped" => OrderStatus::已出貨,
        _ => return Err(crate::error::AppError::Validation(
            format!("Invalid status: {}", request.status)
        )),
    };
    
    // 只有管理員可以更新訂單狀態
    let updated_order = state.order_service.update_order_status_admin(order_id, status).await?;
    
    Ok(Json(json!({
        "success": true,
        "message": "Order status updated successfully",
        "data": {
            "order": updated_order
        }
    })))
}

/// 取消訂單
async fn cancel_order(
    State(state): State<AppState>,
    Extension(claims): Extension<crate::api::middleware::Claims>,
    Path(order_id): Path<i64>,
) -> AppResult<Json<Value>> {
    let user_id = claims.user_id;
    
    let cancelled_order = state.order_service.cancel_order(order_id, user_id).await?;
    
    Ok(Json(json!({
        "success": true,
        "message": "Order cancelled successfully",
        "data": {
            "order": cancelled_order
        }
    })))
}

/// 查詢所有用戶的訂單列表（管理員專用）
async fn list_all_orders(
    State(state): State<AppState>,
    Extension(_claims): Extension<crate::api::middleware::Claims>,
    Query(params): Query<OrderQueryParams>,
) -> AppResult<Json<Value>> {
    // 解析篩選條件
    let filter = if params.status.is_some() || params.start_date.is_some() || params.end_date.is_some() {
        let status = params.status.as_ref().and_then(|s| match s.as_str() {
            "pending" => Some(OrderStatus::待處理),
            
            "processing" => Some(OrderStatus::揀貨中),
            "shipped" => Some(OrderStatus::已出貨),
            
            
            _ => None,
        });
        
        let start_date = params.start_date.as_ref()
            .and_then(|s| chrono::DateTime::parse_from_rfc3339(s).ok())
            .map(|dt| dt.with_timezone(&chrono::Utc));
            
        let end_date = params.end_date.as_ref()
            .and_then(|s| chrono::DateTime::parse_from_rfc3339(s).ok())
            .map(|dt| dt.with_timezone(&chrono::Utc));
        
        Some(OrderFilter {
            status,
            start_date,
            end_date,
            ..Default::default()
        })
    } else {
        None
    };
    
    // 管理員查看所有用戶的訂單，所以不指定用戶ID
    let orders = state.order_service.list_all_orders_with_users(
        filter,
        params.page.unwrap_or(1),
        params.limit.unwrap_or(20)
    ).await?;
    
    Ok(Json(json!({
        "success": true,
        "data": {
            "orders": orders
        }
    })))
}

/// 更新訂單項目缺貨狀態（管理員專用）
async fn update_order_item_stock_status(
    State(state): State<AppState>,
    Extension(_claims): Extension<crate::api::middleware::Claims>,
    Path(item_id): Path<i64>,
    JsonExtractor(request): JsonExtractor<UpdateOrderItemStockStatusRequest>,
) -> AppResult<Json<Value>> {
    let updated_item = state.order_service.update_order_item_stock_status(item_id, request.is_stock_out).await?;
    
    Ok(Json(json!({
        "success": true,
        "message": format!("Order item stock status updated to {}", if request.is_stock_out { "out of stock" } else { "in stock" }),
        "data": {
            "item": updated_item
        }
    })))
}